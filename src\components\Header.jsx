import React, { useState, useRef, useEffect } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import MakonisLogo from "../Asserts/Makonis-Logo.png";
import SearchOverlay from "./SearchOverlay";


const Header = () => {
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
    const [openDropdown, setOpenDropdown] = useState('');
    const [isDesktopSearchOpen, setIsDesktopSearchOpen] = useState(false);
    const [isMobileSearchOpen, setIsMobileSearchOpen] = useState(false);
    const [openMobileSubMenu, setOpenMobileSubMenu] = useState('');
    const [openMobileSubDropdown, setOpenMobileSubDropdown] = useState('');
    const [activeSubMenu, setActiveSubMenu] = useState('');

    const location = useLocation();
    const navigate = useNavigate();
    const headerRef = useRef(null);
    const desktopSearchRef = useRef(null);
    const menuTimeoutRef = useRef(null);

    const transitionAllDuration500EaseOut = 'transition-all duration-500 ease-out';

    const toggleMobileMenu = () => {
        setMobileMenuOpen(!mobileMenuOpen);
    };

    const toggleMobileSubMenu = (menuName) => {
        setOpenMobileSubMenu(openMobileSubMenu === menuName ? '' : menuName);
        // Close any open sub-dropdowns when main menu changes
        if (openMobileSubMenu !== menuName) {
            setOpenMobileSubDropdown('');
        }
    };

    const toggleMobileSubDropdown = (dropdownName) => {
        setOpenMobileSubDropdown(openMobileSubDropdown === dropdownName ? '' : dropdownName);
    };

    const handleMenuClose = () => {
        menuTimeoutRef.current = setTimeout(() => {
            setOpenDropdown('');
        }, 200);
    }

    const handleMenuEnter = () => {
        clearTimeout(menuTimeoutRef.current);
    }

    const [searchQuery, setSearchQuery] = useState('');
    const [searchResults, setSearchResults] = useState([]);
    const [isSearching, setIsSearching] = useState(false);

    const handleDesktopSearch = async (query) => {
        setSearchQuery(query);

        if (!query || query.trim().length < 2) {
            setSearchResults([]);
            setIsSearching(false);
            return;
        }

        setIsSearching(true);
        try {
            // Import search utilities dynamically
            const { getSearchSuggestions } = await import('../contexts/searchUtils');
            const results = getSearchSuggestions(query.trim(), 8);
            setSearchResults(results);
        } catch (error) {
            console.error('Search error:', error);
            setSearchResults([]);
        } finally {
            setIsSearching(false);
        }
    }

    const handleSearchResultClick = (result) => {
        navigate(result.path);
        setIsDesktopSearchOpen(false);
        setSearchQuery('');
        setSearchResults([]);
    }

    useEffect(() => {
        if (headerRef.current) {
            headerRef.current.style.opacity = '0';
            headerRef.current.style.transform = 'translateY(-20px)';
            setTimeout(() => {
                if (headerRef.current) {
                    headerRef.current.style.transition = 'all 0.8s ease';
                    headerRef.current.style.opacity = '1';
                    headerRef.current.style.transform = 'translateY(0)';
                }
            }, 100);
        }
    }, []);

    useEffect(() => {
        const handleClickOutside = (event) => {
            // Close any open dropdown if clicking outside
            if (!event.target.closest('.dropdown')) {
                setOpenDropdown('');
            }
            if (desktopSearchRef.current && !desktopSearchRef.current.contains(event.target)) {
                setIsDesktopSearchOpen(false);
            }
        };
        document.addEventListener("mousedown", handleClickOutside);
        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    useEffect(() => {
        document.body.style.overflow = mobileMenuOpen ? 'hidden' : 'unset';
        return () => {
            document.body.style.overflow = 'unset';
        };
    }, [mobileMenuOpen]);

    const navItems = [
        { name: "Home", path: "/" },
        { name: "Who we are", path: "/about-us" },
        {
            name: "Business",
            path: "/business",
            isMegaMenu: true, // For desktop mega menu
            megaMenuItems: [
                {
                    name: "IT Services",
                    path: "/services",
                    subMenuItems: [
                        { name: "Staff Augmentation", path: "/services/staff-augmentation" },
                        { name: "Enterprise RPO", path: "/services/enterprise-rpo" },
                        { name: "Contract Staffing", path: "/services/contract-staffing" },
                        { name: "Hire Train Deploy", path: "/services/hire-train-deploy" },
                        { name: "Integration", path: "/services/integration" },
                        { name: "Testing", path: "/services/testing" },
                    ]
                },
                {
                    name: "Products",
                    path: "/products",
                    subMenuItems: [
                        { name: "IoT", path: "/products/iot" },
                        { name: "AI", path: "/products/ai" },
                        { name: "ATS Demo", path: "/ats-demo" },
                    ]
                },
                {
                    name: "Semiconductors",
                    path: "/semiconductors",
                    subMenuItems: [
                        { name: "Physical Design", path: "/semiconductors/physical-design" },
                        { name: "Physical Verification", path: "/semiconductors/physical-verification" },
                    ]
                }
            ]
        },
        { name: "Case Studies", path: "/case-studies" },
        { name: "Client testimonials", path: "/testimonials" },
        { name: "Careers", path: "/careers" },
        { name: "Contact Us", path: "/contact" },
    ];

    // Mobile navigation items (Business contains sub-dropdowns)
    const mobileNavItems = [
        { name: "Home", path: "/" },
        { name: "Who we are", path: "/about-us" },
        {
            name: "Business",
            path: "/business",
            isDropdown: true,
            dropdownItems: [
                {
                    name: "IT Services",
                    path: "/services",
                    isSubDropdown: true,
                    subDropdownItems: [
                        { name: "Staff Augmentation", path: "/services/staff-augmentation" },
                        { name: "Enterprise RPO", path: "/services/enterprise-rpo" },
                        { name: "Contract Staffing", path: "/services/contract-staffing" },
                        { name: "Hire Train Deploy", path: "/services/hire-train-deploy" },
                        { name: "Integration", path: "/services/integration" },
                        { name: "Testing", path: "/services/testing" },
                    ]
                },
                {
                    name: "Products",
                    path: "/products",
                    isSubDropdown: true,
                    subDropdownItems: [
                        { name: "IoT", path: "/products/iot" },
                        { name: "AI", path: "/products/ai" },
                        { name: "ATS Demo", path: "/ats-demo" },
                    ]
                },
                {
                    name: "Semiconductors",
                    path: "/semiconductors",
                    isSubDropdown: true,
                    subDropdownItems: [
                        { name: "Physical Design", path: "/semiconductors/physical-design" },
                        { name: "Physical Verification", path: "/semiconductors/physical-verification" },
                    ]
                }
            ]
        },
        { name: "Case Studies", path: "/case-studies" },
        { name: "Client testimonials", path: "/testimonials" },
        { name: "Careers", path: "/careers" },
        { name: "Contact Us", path: "/contact" },
    ];

    // Get submenu data for mega menu
    const businessMenuItem = navItems.find(item => item.name === 'Business');
    const subMenuData = businessMenuItem?.megaMenuItems?.find(sub => sub.name === activeSubMenu);

    const headerStyle = {
        background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%)',
        backdropFilter: 'blur(20px)',
        boxShadow: '0 4px 24px rgba(0, 41, 86, 0.08)',
        padding: '8px 0',
        transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
        borderBottom: '1px solid rgba(0, 41, 86, 0.08)',
        zIndex: 1050,
        position: 'sticky',
        top: 0,
        width: '100%',
    };


    
    return (
        <>
            <style>
                {`
                /* ===== Mobile Sidebar Styles ===== */
                .mobile-menu-backdrop { position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0, 21, 41, 0.6); z-index: 1150; opacity: 0; transition: opacity 0.4s ease; pointer-events: none; }
                .mobile-menu-backdrop.open { opacity: 1; pointer-events: auto; }
                .mobile-sidebar { position: fixed; top: 0; left: 0; height: 100%; width: 300px; background: #fff; z-index: 1200; transform: translateX(-100%); transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1); display: flex; flex-direction: column; }
                .mobile-sidebar.open { transform: translateX(0); }
                .mobile-sidebar-header { padding: 1rem 1.5rem; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #e5e7eb; }
                .mobile-sidebar-header .close-btn { background: none; border: none; font-size: 1.5rem; color: #334155; cursor: pointer; }
                .mobile-nav-list { padding: 1rem; list-style: none; margin: 0; overflow-y: auto; flex-grow: 1; }
                .mobile-nav-link, .mobile-submenu-toggle { display: flex; justify-content: space-between; align-items: center; padding: 0.75rem 1rem; text-decoration: none; color: #002956; font-weight: 500; border-radius: 6px; transition: background-color 0.2s ease, color 0.2s ease; cursor: pointer; }
                .mobile-nav-link:hover, .mobile-submenu-toggle:hover { background-color: #f1f5f9; }
                .mobile-nav-link.active { color: #00a0e9; background-color: #e0f2fe; }
                .mobile-submenu { list-style: none; padding-left: 1rem; overflow: hidden; max-height: 0; transition: max-height 0.4s ease-in-out; }
                .mobile-submenu.open { max-height: 500px; }
                .mobile-submenu-link { display: block; padding: 0.6rem 1rem; text-decoration: none; color: #334155; border-radius: 6px; }
                .mobile-submenu-link:hover { background-color: #f1f5f9; }
                .mobile-submenu-toggle .fa-chevron-down { transition: transform 0.3s ease; }
                .mobile-submenu-toggle.open .fa-chevron-down { transform: rotate(180deg); }
                .mobile-sub-dropdown { list-style: none; padding-left: 1rem; overflow: hidden; max-height: 0; transition: max-height 0.4s ease-in-out; }
                .mobile-sub-dropdown.open { max-height: 300px; }
                .mobile-sub-dropdown-toggle { display: flex; justify-content: space-between; align-items: center; padding: 0.6rem 1rem; text-decoration: none; color: #334155; border-radius: 6px; transition: background-color 0.2s ease, color 0.2s ease; cursor: pointer; margin-left: 0.5rem; }
                .mobile-sub-dropdown-toggle:hover { background-color: #f1f5f9; }
                .mobile-sub-dropdown-toggle .fa-chevron-down { transition: transform 0.3s ease; }
                .mobile-sub-dropdown-toggle.open .fa-chevron-down { transform: rotate(180deg); }
                .mobile-sub-dropdown-link { display: block; padding: 0.5rem 1rem; text-decoration: none; color: #6b7280; border-radius: 6px; margin-left: 1rem; }
                .mobile-sub-dropdown-link:hover { background-color: #f1f5f9; }

                /* ===== Desktop Styles ===== */
                .desktop-nav-link { color: #002956; font-weight: 600; font-size: 15px; letter-spacing: 0.5px; padding: 12px 16px; position: relative; text-decoration: none; display: block; white-space: nowrap; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); border-radius: 8px; }
                .desktop-nav-link.active { color: #00a0e9; }
                .desktop-search-item{position:relative}.desktop-search-dropdown{position:absolute;top:calc(100% + 15px);right:0;z-index:1100;width:400px;background:#fff;border-radius:12px;box-shadow:0 10px 40px rgba(0,41,86,.15),0 0 0 1px rgba(0,0,0,.05);padding:8px;opacity:0;visibility:hidden;transform:translateY(10px) scale(.98);transform-origin:top right;transition:all .3s cubic-bezier(.4,0,.2,1)}.desktop-search-dropdown.open{opacity:1;visibility:visible;transform:translateY(0) scale(1)}.desktop-search-wrapper{position:relative}.desktop-search-input{width:100%;height:48px;padding-left:45px;padding-right:45px;font-size:15px;color:#002956;border:1px solid #e0e7ff;border-radius:8px;background-color:#f8fafc;transition:all .3s cubic-bezier(.4,0,.2,1)}.desktop-search-input:focus{outline:0;border-color:#00a0e9;background-color:#fff;box-shadow:0 0 0 3px rgba(0,160,233,.2)}.desktop-search-input::placeholder{color:#8da3b9}.desktop-search-icon,.desktop-search-close-btn{position:absolute;top:50%;transform:translateY(-50%);color:#55708c;background:0 0;border:none;cursor:pointer;transition:color .3s ease}.desktop-search-icon{left:15px;font-size:16px}.desktop-search-close-btn{right:12px;font-size:18px;padding:5px}.desktop-search-close-btn:hover{color:#002956}
                .main-menu-panel{flex:0 0 45%;border-right:1px solid rgba(0,41,86,.1);padding-right:12px}.sub-menu-panel{flex:1 1 auto;padding-left:20px}.sub-menu-panel-content{animation:fadeIn .4s ease}@keyframes fadeIn{0%{opacity:0;transform:translateX(10px)}to{opacity:1;transform:translateX(0)}}.main-menu-item{display:flex;justify-content:space-between;align-items:center;padding:12px 16px;border-radius:8px;text-decoration:none;font-weight:500;color:#002956;transition:all .2s ease-in-out;cursor:pointer}.main-menu-item.active,.main-menu-item:hover{background-color:rgba(0,160,233,.1);color:#00a0e9;transform:translateX(4px)}.sub-menu-item{display:block;padding:10px 16px;border-radius:6px;text-decoration:none;font-weight:400;color:#334155;transition:all .2s ease-in-out}.sub-menu-item:hover{background-color:rgba(0,41,86,.05);color:#002956}.sub-menu-panel h3{font-size:1rem;font-weight:600;color:#002956;margin-bottom:1rem;padding-bottom:.5rem;border-bottom:1px solid rgba(0,41,86,.1)}
                `}
            </style>

            <header className="sticky-top" style={headerStyle} ref={headerRef}>
                <div className="container-fluid px-4 lg:px-8 xl:px-12 d-flex justify-content-between align-items-center">
                    <Link to="/" className="navbar-brand">
                        <img src={MakonisLogo} alt="Makonis Software" className={`${transitionAllDuration500EaseOut} h-12 w-auto`} />
                    </Link>

                    {/* Desktop Navigation */}
                    <nav className="d-none d-lg-flex align-items-center">
                        <ul className="navbar-nav flex-row">
                            {navItems.map((item) => (
                                <li
                                    key={item.name}
                                    className="nav-item dropdown"
                                    onMouseLeave={item.isMegaMenu ? handleMenuClose : undefined}
                                >
                                    {item.isMegaMenu ? (
                                        <>
                                            <a
                                                href="#"
                                                className={`desktop-nav-link ${location.pathname.startsWith(item.path) ? "active" : ""}`}
                                                onMouseEnter={(e) => {
                                                    e.preventDefault();
                                                    handleMenuEnter();
                                                    setOpenDropdown(item.name);
                                                    setActiveSubMenu('IT Services'); // Default to first item
                                                }}
                                                onClick={(e) => e.preventDefault()}
                                            >
                                                {item.name}
                                                <lord-icon
                                                    src="https://cdn.lordicon.com/xcrjfuzb.json"
                                                    trigger="hover"
                                                    style={{ width: '16px', height: '16px', marginLeft: '8px' }}>
                                                </lord-icon>
                                            </a>
                                            <div
                                                className={`mega-menu ${openDropdown === item.name ? 'show' : ''}`}
                                                style={{
                                                    position: 'absolute',
                                                    top: '100%',
                                                    left: '50%',
                                                    transform: 'translateX(-50%)',
                                                    background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.99) 0%, rgba(248, 250, 252, 0.99) 100%)',
                                                    backdropFilter: 'blur(20px)',
                                                    borderRadius: '16px',
                                                    boxShadow: '0 20px 40px rgba(0, 41, 86, 0.2), 0 8px 16px rgba(0, 160, 233, 0.1)',
                                                    border: '1px solid rgba(0, 41, 86, 0.1)',
                                                    width: '600px',
                                                    display: 'flex',
                                                    padding: '12px',
                                                    marginTop: '16px',
                                                    zIndex: 1000,
                                                    visibility: openDropdown === item.name ? 'visible' : 'hidden',
                                                    opacity: openDropdown === item.name ? 1 : 0,
                                                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
                                                }}
                                                onMouseEnter={handleMenuEnter}
                                                onMouseLeave={handleMenuClose}
                                            >
                                                <div className="main-menu-panel" style={{ flex: '0 0 45%', borderRight: '1px solid rgba(0, 41, 86, 0.1)', paddingRight: '12px' }}>
                                                    {item.megaMenuItems.map(mainItem => (
                                                        <div
                                                            key={mainItem.name}
                                                            className={`main-menu-item ${activeSubMenu === mainItem.name ? 'active' : ''}`}
                                                            style={{
                                                                display: 'flex',
                                                                justifyContent: 'space-between',
                                                                alignItems: 'center',
                                                                padding: '12px 16px',
                                                                borderRadius: '8px',
                                                                textDecoration: 'none',
                                                                fontWeight: '500',
                                                                color: activeSubMenu === mainItem.name ? '#00a0e9' : '#002956',
                                                                backgroundColor: activeSubMenu === mainItem.name ? 'rgba(0, 160, 233, 0.1)' : 'transparent',
                                                                transition: 'all 0.2s ease-in-out',
                                                                cursor: 'pointer'
                                                            }}
                                                            onMouseEnter={() => setActiveSubMenu(mainItem.name)}
                                                        >
                                                            <span>{mainItem.name}</span>
                                                            <lord-icon
                                                                src="https://cdn.lordicon.com/zmkotitn.json"
                                                                trigger="hover"
                                                                style={{ width: '14px', height: '14px' }}>
                                                            </lord-icon>
                                                        </div>
                                                    ))}
                                                </div>
                                                <div className="sub-menu-panel" style={{ flex: '1 1 auto', paddingLeft: '20px' }}>
                                                    {subMenuData && (
                                                        <div className="sub-menu-panel-content">
                                                            <h3 style={{ fontSize: '1rem', fontWeight: '600', color: '#002956', marginBottom: '1rem', paddingBottom: '0.5rem', borderBottom: '1px solid rgba(0, 41, 86, 0.1)' }}>
                                                                {subMenuData.name}
                                                            </h3>
                                                            {subMenuData.subMenuItems.map(subItem => (
                                                                <Link
                                                                    to={subItem.path}
                                                                    key={subItem.name}
                                                                    className="sub-menu-item"
                                                                    style={{
                                                                        display: 'block',
                                                                        padding: '10px 16px',
                                                                        borderRadius: '6px',
                                                                        textDecoration: 'none',
                                                                        fontWeight: '400',
                                                                        color: '#334155',
                                                                        transition: 'all 0.2s ease-in-out'
                                                                    }}
                                                                    onClick={() => setOpenDropdown('')}
                                                                >
                                                                    {subItem.name}
                                                                </Link>
                                                            ))}
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        </>
                                    ) : (
                                        <Link
                                            to={item.path}
                                            className={`desktop-nav-link ${location.pathname === item.path ? "active" : ""}`}
                                        >
                                            {item.name}
                                        </Link>
                                    )}
                                </li>
                            ))}
                            <li className="nav-item ms-2 desktop-search-item" ref={desktopSearchRef}>
                                <button className="btn" onClick={(e) => { e.stopPropagation(); setIsDesktopSearchOpen(!isDesktopSearchOpen); }} style={{ minWidth: '44px', minHeight: '44px', border: '2px solid #002956', background: isDesktopSearchOpen ? '#002956' : '#00a0e9', borderRadius: '8px', color: 'white', display: 'flex', alignItems: 'center', justifyContent: 'center', transition: 'all 0.3s ease' }}>
                                    <lord-icon
                                        src={isDesktopSearchOpen ? "https://cdn.lordicon.com/nqtddedc.json" : "https://cdn.lordicon.com/xfftupfv.json"}
                                        trigger="hover"
                                        style={{ width: '20px', height: '20px' }}>
                                    </lord-icon>
                                </button>
                                <div className={`desktop-search-dropdown ${isDesktopSearchOpen ? 'open' : ''}`}>
                                    <div className="desktop-search-wrapper">
                                        <lord-icon
                                            src="https://cdn.lordicon.com/xfftupfv.json"
                                            trigger="hover"
                                            class="desktop-search-icon"
                                            style={{ width: '18px', height: '18px' }}>
                                        </lord-icon>
                                        <input
                                            type="text"
                                            placeholder="Search..."
                                            className="desktop-search-input"
                                            autoFocus
                                            value={searchQuery}
                                            onClick={(e) => e.stopPropagation()}
                                            onChange={(e) => handleDesktopSearch(e.target.value)}
                                            onKeyDown={(e) => {
                                                if (e.key === 'Escape') {
                                                    setIsDesktopSearchOpen(false);
                                                    setSearchQuery('');
                                                    setSearchResults([]);
                                                }
                                            }}
                                        />
                                        {isSearching && (
                                            <div className="position-absolute" style={{ right: '15px', top: '50%', transform: 'translateY(-50%)' }}>
                                                <div className="spinner-border spinner-border-sm text-primary" role="status">
                                                    <span className="visually-hidden">Searching...</span>
                                                </div>
                                            </div>
                                        )}
                                    </div>

                                    {/* Search Results Dropdown */}
                                    {isDesktopSearchOpen && searchQuery.length >= 2 && (
                                        <div className="search-results-dropdown" style={{
                                            marginTop: '8px',
                                            background: '#fff',
                                            borderRadius: '8px',
                                            boxShadow: '0 4px 12px rgba(0, 41, 86, 0.15)',
                                            border: '1px solid #e0e7ff',
                                            maxHeight: '300px',
                                            overflowY: 'auto'
                                        }}>
                                            {searchResults.length > 0 ? (
                                                <>
                                                    <div className="px-3 py-2 border-bottom" style={{ fontSize: '12px', color: '#6b7280', fontWeight: '600' }}>
                                                        Search Results
                                                    </div>
                                                    {searchResults.map((result, index) => (
                                                        <div
                                                            key={result.id}
                                                            className="search-result-item"
                                                            style={{
                                                                padding: '12px 16px',
                                                                cursor: 'pointer',
                                                                borderBottom: index < searchResults.length - 1 ? '1px solid #f1f5f9' : 'none',
                                                                transition: 'background-color 0.2s ease'
                                                            }}
                                                            onClick={() => handleSearchResultClick(result)}
                                                            onMouseEnter={(e) => e.target.style.backgroundColor = '#f8fafc'}
                                                            onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
                                                        >
                                                            <div className="d-flex align-items-center">
                                                                <lord-icon
                                                                    src="https://cdn.lordicon.com/kiynvdns.json"
                                                                    trigger="hover"
                                                                    colors="primary:#0d6efd"
                                                                    style={{ width: '16px', height: '16px', marginRight: '12px' }}>
                                                                </lord-icon>
                                                                <div className="flex-grow-1">
                                                                    <div style={{ fontWeight: '500', color: '#002956', fontSize: '14px' }}>{result.title}</div>
                                                                    <div style={{ fontSize: '12px', color: '#6b7280', textTransform: 'capitalize' }}>{result.type}</div>
                                                                </div>
                                                                <lord-icon
                                                                    src="https://cdn.lordicon.com/zmkotitn.json"
                                                                    trigger="hover"
                                                                    colors="primary:#9ca3af"
                                                                    style={{ width: '14px', height: '14px' }}>
                                                                </lord-icon>
                                                            </div>
                                                        </div>
                                                    ))}
                                                </>
                                            ) : searchQuery.length >= 2 && !isSearching ? (
                                                <div className="text-center py-4" style={{ color: '#6b7280' }}>
                                                    <lord-icon
                                                        src="https://cdn.lordicon.com/xfftupfv.json"
                                                        trigger="hover"
                                                        style={{ width: '32px', height: '32px', display: 'block', marginBottom: '8px' }}>
                                                    </lord-icon>
                                                    <div>No results found</div>
                                                    <small>Try a different search term</small>
                                                </div>
                                            ) : null}
                                        </div>
                                    )}
                                </div>
                            </li>
                        </ul>
                    </nav>

                    {/* Mobile Search and Hamburger Icons */}
                    <div className="d-lg-none d-flex align-items-center">
                        {/* Mobile Search Icon */}
                        <button
                            className="btn me-2"
                            type="button"
                            onClick={() => setIsMobileSearchOpen(true)}
                            style={{
                                minWidth: '44px',
                                minHeight: '44px',
                                border: '2px solid #002956',
                                background: '#00a0e9',
                                borderRadius: '8px',
                                color: 'white',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                transition: 'all 0.3s ease'
                            }}
                        >
                            <lord-icon
                                src="https://cdn.lordicon.com/xfftupfv.json"
                                trigger="hover"
                                style={{ width: '20px', height: '20px' }}>
                            </lord-icon>
                        </button>

                        {/* Mobile Hamburger Icon */}
                        <button className="btn" type="button" onClick={toggleMobileMenu}>
                             <lord-icon
                                src="https://cdn.lordicon.com/fihkmkpx.json"
                                trigger="hover"
                                colors="primary:#002956"
                                style={{ width: '24px', height: '24px' }}>
                            </lord-icon>
                        </button>
                    </div>
                </div>
            </header>

            {/* Mobile Sidebar Navigation */}
            <div className={`mobile-menu-backdrop d-lg-none ${mobileMenuOpen ? 'open' : ''}`} onClick={toggleMobileMenu}></div>
            <div className={`mobile-sidebar d-lg-none ${mobileMenuOpen ? 'open' : ''}`}>
                <div className="mobile-sidebar-header">
                    <span className="fw-bold fs-5" style={{color: '#002956'}}>Makonis</span>
                    <button onClick={toggleMobileMenu} className="close-btn">&times;</button>
                </div>
                <ul className="mobile-nav-list">
                    {mobileNavItems.map(item => (
                        <li key={item.name} className="mb-2">
                            {item.isDropdown ? (
                                <>
                                    <div className={`mobile-submenu-toggle ${openMobileSubMenu === item.name ? 'open' : ''}`} onClick={() => toggleMobileSubMenu(item.name)}>
                                        <span>{item.name}</span>
                                        <lord-icon
                                            src="https://cdn.lordicon.com/xcrjfuzb.json"
                                            trigger="hover"
                                            style={{ width: '16px', height: '16px' }}>
                                        </lord-icon>
                                    </div>
                                    <ul className={`mobile-submenu ${openMobileSubMenu === item.name ? 'open' : ''}`}>
                                        {item.dropdownItems.map(subItem => (
                                             <li key={subItem.name}>
                                                 {subItem.isSubDropdown ? (
                                                     <>
                                                         <div className={`mobile-sub-dropdown-toggle ${openMobileSubDropdown === subItem.name ? 'open' : ''}`} onClick={() => toggleMobileSubDropdown(subItem.name)}>
                                                             <span>{subItem.name}</span>
                                                             <lord-icon
                                                                src="https://cdn.lordicon.com/xcrjfuzb.json"
                                                                trigger="hover"
                                                                style={{ width: '14px', height: '14px' }}>
                                                            </lord-icon>
                                                         </div>
                                                         <ul className={`mobile-sub-dropdown ${openMobileSubDropdown === subItem.name ? 'open' : ''}`}>
                                                             {subItem.subDropdownItems.map(subSubItem => (
                                                                 <li key={subSubItem.name}>
                                                                     <Link to={subSubItem.path} className="mobile-sub-dropdown-link" onClick={toggleMobileMenu}>
                                                                         {subSubItem.name}
                                                                     </Link>
                                                                 </li>
                                                             ))}
                                                         </ul>
                                                     </>
                                                 ) : (
                                                     <Link to={subItem.path} className="mobile-submenu-link" onClick={toggleMobileMenu}>
                                                         {subItem.name}
                                                     </Link>
                                                 )}
                                             </li>
                                        ))}
                                    </ul>
                                </>
                            ) : (
                                <Link to={item.path} className={`mobile-nav-link ${location.pathname === item.path ? 'active' : ''}`} onClick={toggleMobileMenu}>
                                    {item.name}
                                </Link>
                            )}
                        </li>
                    ))}
                </ul>
            </div>

            {/* Mobile Search Overlay */}
            <SearchOverlay
                isOpen={isMobileSearchOpen}
                onClose={() => setIsMobileSearchOpen(false)}
            />
        </>
    );
};

export default Header;