import React, { useEffect, useState, useRef } from 'react';
import { toast } from "react-toastify";
import 'react-toastify/dist/ReactToastify.css';
import { useLocation, useNavigate } from "react-router-dom";
import axios from 'axios';
import Modal from "react-modal";
import 'bootstrap/dist/css/bootstrap.min.css';
import { FaDownload, FaTimes, FaEye, FaPaperPlane, FaSpinner, FaUser, FaBuilding, FaMapMarkerAlt, FaClock, FaMoneyBillWave } from "react-icons/fa";

const Careers = () => {

  const [jobPosts, setJobPosts] = useState([]);

  useEffect(() => {
    axios.get('http://142.93.222.128:8080/api/job_posts')
      .then(response => {
        const transformedJobs = response.data.map((job, index) => {
          const {
            role,
            id,
            client,
            experience_min,
            experience_max,
            skills,
            location,
            mode,
            no_of_positions,
            shift_timings,
            jd_pdf_base64,
            jd_pdf_extension,
            jd_pdf_text
          } = job;
          const jd_raw_clean = typeof jd_pdf_text === 'string'
            ? jd_pdf_text.replace(/^b["']|["']$/g, '').replace(/\\n/g, '\n')
            : '';

          const lines = jd_raw_clean.split('\n');
          let jd_html = '';
          let in_list = false;

          lines.forEach(line => {
            line = line.trim();
            if (!line) return;

            if (/:$/.test(line) || /^[A-Z\s]+$/.test(line)) {
              if (in_list) {
                jd_html += '</ul>';
                in_list = false;
              }
              jd_html += `<h3>${line}</h3>`;
            } else if (/^[•\-*\u2022]+\s*/u.test(line)) {
              if (!in_list) {
                jd_html += '<ul>';
                in_list = true;
              }
              const cleanLine = line.replace(/^[•\-*\u2022]+\s*/u, '');
              jd_html += `<li>${cleanLine}</li>`;
            } else if (/^[A-Z]/.test(line) && line.length < 150) {
              if (in_list) {
                jd_html += '</ul>';
                in_list = false;
              }
              jd_html += `<p>${line}</p>`;
            } else {
              if (in_list) {
                jd_html += '</ul>';
                in_list = false;
              }
              jd_html += `<p>${line}</p>`;
            }
          });


          if (in_list) jd_html += '</ul>';
          return {
            jobtitle: role,
            jobid: id,
            client,
            min: experience_min,
            max: experience_max,
            skills,
            loc: location,
            mode,
            position: no_of_positions,
            qal: shift_timings,
            jd_pdf: jd_pdf_base64,
            jd_pdf_extension,
            jd_html,
            downloadFileName: `JD_${role}`,
            hasJD: !!jd_raw_clean.trim()
          };
        });

        setJobPosts(transformedJobs);
      })
      .catch(error => {
        console.error("Error fetching job posts:", error);
      });
  }, []);


  const downloadJD = (base64, filename, extension) => {
    const element = document.createElement("a");
    element.setAttribute("href", `data:application/${extension};base64,${base64}`);
    element.setAttribute("download", `${filename}.${extension}`);
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };


  const [selectedJob, setSelectedJob] = useState(null);// null means no modal open
  // null means no modal open

  const openModal = (job) => {
    //   console.log(jobid);
    setSelectedJob(job);
  };

  const closeModal = () => {
    setSelectedJob(null);
  };

  const [applyJob, setApplyJob] = useState(null);

  const openapplyModal = (job) => {
    //  console.log(job);
    setApplyJob(job);
  };

  const closeapplyModal = () => {
    setApplyJob(null);
    // setApplyform(false)
  };

  const [applyform, setApplyform] = useState(false);
  const [selectedJobId, setSelectedjobId] = useState('');
  const [selectedprofile, setSelectedprofile] = useState('');
  const [selectedclient, setSelectedclient] = useState('');
  // const [selected, setSelected] = useState(false);



  const handleCloseModal = () => {
    setApplyform(false)
       setDetails({
          name: '',
          mobile: '',
          email: '',
          total_experience_years: '',
          total_experience_months: '',
          relevant_experience_years: '',
          relevant_experience_months: '',
          qualifications: '',
          last_working_date: '',
          serving_notice_period: '',
          current_ctc_type: '',
          current_ctc_value: '',
          expected_ctc_type: '',
          expected_ctc_value: '',
          skills: '',
          linkedin: "",
          remarks: "",
          holding_offer: "",
          highest_package: "",
          period_of_notice: "", // 1 Month, 3 Months etc..
          total_offers: "",
        });

        setSelectedFile('');
        setSelectedjobId(null);
        setSelectedprofile('');
  
    
  }

  const handleOpenModal = (applyJob) => {
    console.log(applyJob)
    console.log(applyJob.client)
    setSelectedjobId(applyJob.jobid)
    setSelectedprofile(applyJob.jobtitle)
    setSelectedclient(applyJob.client)
    setApplyform(true)
  }
  // console.log(selectedprofile)
  // console.log(selectedJobId)

  // add candidate functionalites

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const nameRegex = /^[A-Za-z\s]+$/;
  const [closeNProceedRef, setCloseNProceedRef] = useState(null)
  const hidden_ref = useRef();
  const [profileValue, setProfileValue] = useState("");
  const location = useLocation();
  const navigate = useNavigate();
  const [showDetails, setShowDetails] = useState(false);
  const [showDuplicate1, setShowDuplicate1] = useState(false);
  const [showDuplicate2, setShowDuplicate2] = useState(false);
  const [duplicate, setDuplicate] = useState([]);
  const [duplicateCandidate, setDuplicateCandidate] = useState([]);
  const notify = () => toast.success("Candidate added successfully");
  const notifyError = () =>
    toast.error("Unable to add candidate, Please try again.");
  const [showConfirmationWindow, setShowConfirmationWindow] = useState();
  // const [ allJobs, setAllJobs ] = useState(null);
  const [waitForSubmission, setwaitForSubmission] = useState(false);
  const [waitForSubmission2, setwaitForSubmission2] = useState(false);
  const initialState = {
    job_id:
      "",
    name: "",
    mobile: "",
    email: "",
    client: "",
    // profile: location.state.profile,
    skills: "",
    qualifications: "",
    reason_for_job_change: "",
    resume: null,
    current_company: "",
    position: "",
    current_job_location: "",
    preferred_job_location: "",
    total_experience_years: "",
    total_experience_months: "",
    relevant_experience_years: "",
    relevant_experience_months: "",
    current_ctc_type: "INR",
    current_ctc_value: "",
    expected_ctc_type: "INR",
    expected_ctc_value: "",
    serving_notice_period: "", //serving notice period
    holding_offer: "",
    period_of_notice: "", // 1 Month, 3 Months etc..
    total_offers: "",
    linkedin: "",
    remarks: "",
    last_working_date: "",
    buyout: false,
    highest_package: "",
  };
  const exp_yrs = new Array(21).fill(0).map((_, idx) => idx);
  const exp_months = new Array(12).fill(0).map((_, idx) => idx);
  const [selectedFile, setSelectedFile] = useState(null);
  const [candidate_details, setDetails] = useState(initialState);


  // useEffect(() => {
  //   // console.log("lcoation", location)
  //   if (location.state) {
  //     setDetails((prev) => ({
  //       ...prev,
  //       client: location.state.client,
  //     }));
  //   }
  // }, [location]);

  const handleCloseDetails = () => {
    setwaitForSubmission(false);
    setShowDetails(false);
  };
  // useEffect(() => {
  //     console.log(candidate_details);
  // }, [ candidate_details ]);

  // const handleCloseNProceed = (id) => {
  //   setShowDuplicate1(false);
  //   // setShowDetails(false);
  //   confirmAddCandidate();
  //   setCloseNProceedRef(id)
  // };

  const handleChange = (e) => {
    if (e.target.name === "buyout") {
      setDetails({ ...candidate_details, buyout: e.target.checked });
      return;
    }

    if (e.target.name === "qualifications") {
      const alphanumericValue = e.target.value.replace(/[^a-zA-Z0-9 ]/g, "");
      setDetails({ ...candidate_details, qualifications: alphanumericValue });
    } else {
      setDetails({ ...candidate_details, [e.target.name]: e.target.value });
    }

    // if(e.target.value == 'Select a Job ID'){
    //     setProfileValue('');
    // }else{
    //     console.log(location?.state.role)
    //     setProfileValue(location?.state.role);
    // }
  };

  const handleJobId = (e) => {
    setDetails({ ...candidate_details, [e.target.name]: e.target.value });
    if (e.target.value == "") {
      setProfileValue("");
    } else {
      const val = e.target.value;
      // console.log(location?.state.obj[val]);
      setProfileValue(location?.state.obj[val]);
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    setSelectedFile(file);
  };



  const fileToBase64 = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        resolve(reader.result.split(",")[1]);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  };

  const notify1 = () => toast.error("resume is mandatory");

  const notify2 = () => toast.warn("Email format is incorrect");

  const isValidEmail = (email) => {
    return emailRegex.test(email);
  };

  const isValidName = (name) => {
    return nameRegex.test(name);
  };
console.log(selectedclient)
  const confirmAddCandidate = async () => {

    try {
      const base64String = await fileToBase64(selectedFile);
      const add_candidate_data = {
        ...candidate_details,
        job_id: selectedJobId,
        client:selectedclient,
        user_id: "51",
        from_page : "makoniscareer",
        profile: selectedprofile,
        resume: base64String,
        expected_ctc: `${candidate_details.expected_ctc_type} ${candidate_details.expected_ctc_value}`,
        current_ctc: `${candidate_details.current_ctc_type} ${candidate_details.current_ctc_value}`,
        experience: `${candidate_details.total_experience_years}.${candidate_details.total_experience_months}`,
        relevant_experience: `${candidate_details.relevant_experience_years}.${candidate_details.relevant_experience_months}`,
      };

      const response = await fetch("http://142.93.222.128:8080/add_candidate", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(add_candidate_data),
      });

      const data = await response.json();

      if (data.status !== "error") {
        toast.success("Candidate added successfully!");
        setDetails({
          name: '',
          mobile: '',
          email: '',
          total_experience_years: '',
          total_experience_months: '',
          relevant_experience_years: '',
          relevant_experience_months: '',
          qualifications: '',
          last_working_date: '',
          serving_notice_period: '',
          current_ctc_type: '',
          current_ctc_value: '',
          expected_ctc_type: '',
          expected_ctc_value: '',
          skills: '',
          linkedin: "",
          remarks: "",
          holding_offer: "",
          highest_package: "",
          period_of_notice: "", // 1 Month, 3 Months etc..
          total_offers: "",
        });

        setSelectedFile('');
        setSelectedjobId(null);
        setSelectedprofile('');
        setApplyform(false)
        closeapplyModal()

        // Optionally: navigate somewhere
      } else {
        notifyError();
      }
    } catch (err) {
      console.error(err);
      notifyError();
    } finally {
      // always reset flag
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setwaitForSubmission(true);

    const candidate_data = {
      name: candidate_details.name ? candidate_details.name.trim() : '',
      mobile: candidate_details.mobile ? candidate_details.mobile.trim() : '',
      email: candidate_details.email ? candidate_details.email.trim() : '',
      total_experience_years: candidate_details.total_experience_years,
      total_experience_months: candidate_details.total_experience_months,
      relevant_experience_years: candidate_details.relevant_experience_years,
      relevant_experience_months: candidate_details.relevant_experience_months,
      qualifications: candidate_details.qualifications ? candidate_details.qualifications.trim() : '',
      last_working_date: candidate_details.last_working_date,
      serving_notice_period: candidate_details.serving_notice_period,
    };

    const currentDate = new Date().setHours(0, 0, 0, 0);
    const selectedDate = new Date(candidate_details.last_working_date).setHours(
      0,
      0,
      0,
      0,
    );

    if (!selectedFile) {
      notifyAlert();
      setwaitForSubmission(false);
      return;
    }

    if (selectedDate < currentDate) {
      toast.warn("Last working date cannot be in past");
      setwaitForSubmission(false);
      return;
    }
    if (!candidate_details.current_ctc_value) {
      toast.warn("Current CTC is required.");
      setwaitForSubmission(false);
      return;
    }
    if (Number(candidate_details.expected_ctc_value) <= Number(candidate_details.current_ctc_value)) {
      toast.warn("Expected CTC should be greater than Current CTC");
      setwaitForSubmission(false);
      return;
    }
    if (!candidate_details.expected_ctc_value) {
      toast.warn("Expected CTC is required.");
      setwaitForSubmission(false);
      return;
    }


    const qualifications = candidate_data.qualifications?.trim() || '';
    const containsOnlyNumbers = /^\d+$/.test(qualifications);
    if (containsOnlyNumbers) {
      toast.warn("Qualifications must contain alphabets along with numbers");
      setwaitForSubmission(false);
      return;
    }

    if (!isValidMobileNumber(candidate_data.mobile)) {
      toast.warn("Mobile number must contain 10 digits");
      setwaitForSubmission(false);
      return;
    }
    if (!isValidName(candidate_data.name)) {
      toast.warn("Candidate name  should contain alphabetics only.");
      setwaitForSubmission(false);
      return;
    }
    if (!isValidEmail(candidate_data.email)) {
      toast.warn("Please enter a valid email address");
      setwaitForSubmission(false);
      return;
    }

    const totalExperienceYears = candidate_data.total_experience_years;
    const totalExperienceMonths = candidate_data.total_experience_months;
    const relevantExperienceYears = candidate_data.relevant_experience_years;
    const relevantExperienceMonths = candidate_data.relevant_experience_months;

    if (Number(relevantExperienceYears) > Number(totalExperienceYears)) {
      // console.log("lmk", Number(relevantExperienceYears)>Number(totalExperienceYears))
      toast.warn("Relevant experience must be less than total experience");
      setwaitForSubmission(false);
      return;
    } else if (
      Number(relevantExperienceYears) === Number(totalExperienceYears)
    ) {
      if (Number(relevantExperienceMonths) > Number(totalExperienceMonths)) {
        // console.log("el", relevantExperienceYears, totalExperienceYears)
        setwaitForSubmission(false);
        toast.warn("Relevant experience must be less than total experience");
        return;
      }
    }
    console.log("candidate_data", candidate_data);
    try {
      const response = await fetch(
        "http://142.93.222.128:8080/check_candidate",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/JSON",
          },
          body: JSON.stringify(candidate_data),
        },
      );
      const data = await response.json();
      if (!response.ok) {
        throw new Error("Network response was not ok");

      }

//       if (data.message === 
// "Candidate with this mobile or email already exists.") {
//         toast.error(data.message); // Or show a popup if needed
//         setwaitForSubmission(false);
//         return;
//       }
   
//       toast.warn(data.message);
//       confirmAddCandidate();

    // Convert all returned jobIds to strings for safe comparison
    const backendJobIds = data.jobIds.map(id => String(id));
    const currentJobId = String(selectedJobId); // your selected job ID from UI

    if (backendJobIds.includes(currentJobId)) {
      // Job ID already exists — don't allow submission
toast.error(`${data.message}\nClients: ${data.clients.join(', ')}`);

  //  toast.warn(data.message);
      setwaitForSubmission(false);
      return;
    } else {
      // Candidate exists, but not for this job ID — allow submission
      //  toast.info("Candidate exists, but being added for a new job.");
      confirmAddCandidate(); // proceed
  }

    } catch (error) {
      console.log("error", error);
        setwaitForSubmission(false);
    }
  };



  const isValidMobileNumber = (mobileNumber) => {
    return /^\d{10}$/.test(mobileNumber); // Check if the mobile number consists of exactly 10 digits
  };




  const styles1 = {
    marginBottom: "0px",
    fontSize: "13px",
    marginTop: "4px",
    paddingRight: "4px",
    fontWeight: 480,
    color: "#2e2e2e",
    textAlign: window.innerWidth <= 542 ? "left" : "initial",
  };
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
    document.querySelector("body").classList.toggle("active");
  };



  const [isLoading, setIsLoading] = useState(false);
  const notifyAlert = () => toast.warn("Please upload a file for resume.");

  const handleSubmitResume = async (e) => {
    e.preventDefault();
    const file = e.target.files[0];
    // console.log("Selected File:", file);
    setSelectedFile(file);

    // setSelectedFile(file);

    if (!file) {
      notifyAlert();
      return;
    }
    if (isLoading) {
      toast.warn("Resume parsing is already in progress. Please wait.");
      return;
    }
    // Update the selectedFile state with the selected file
    setIsLoading(true);
    try {
      // console.log(file);
      const base64String = await fileToBase64(file);
      // setDetails({
      //   name: "",
      //   email: "",
      //   skills: "",
      //   mobile: "",
      //   current_company: "",
      //   position: "",
      //   current_job_location: "",
      //   qualifications: "",
      // });
      const resumeData = {
        resume: base64String,
      };

      const response = await fetch(
        // "http://192.168.1.7:5002/parse_resume",
        "http://142.93.222.128:8080/parse_resume",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(resumeData),
        },
      );

      if (response.ok) {
        const data = await response.json();
        // console.log(data);
        const { name, mail, skill1, phone, current_company, position, current_job_location, qualifications } = data;
        // console.log(mail);
        const combineSkill = skill1
        if (
          !name &&
          !mail &&
          !combineSkill &&
          !phone &&
          !current_company &&
          !position &&
          !current_job_location &&
          !qualifications
        ) {
          toast.error("No Data Found!"); // Show error toast
          return; // Exit the function
        }
        setDetails({
          ...candidate_details,
          name: name,
          email: mail,
          skills: combineSkill,
          mobile: phone,
          current_company: current_company,
          position: position,
          current_job_location: current_job_location,
          qualifications: qualifications
        });
        toast.success("Resume parsed successfully!");
      }
      else {
        const errorData = await response.json(); // Parse the error response
        const errorMessage = errorData.message || "Unable to parse the uploaded resume.";
        toast.error(errorMessage); // Show backend error message
        // setSelectedFile(null);
        // e.target.value = "";
      }

    } catch (err) {
      console.log(err);
      toast.error(
        "Unable to Parse the uploaded resume. Please change it or enter details manually.",
      );
      // setSelectedFile(null);
      // e.target.value = "";

    } finally {
      setIsLoading(false); // Reset loading state
    }
  };

  const checktoast = () => {
  
     toast.error("Candidate with this mobile or email already exists.", {
    style: {
           background: "#2c3e50",     
       color: "#ecf0f1",           
      fontWeight: "bold",         
      fontSize: "16px",           
      padding: "12px 16px",       
      borderRadius: "6px",         // Rounded corners
     // Optional for emphasis
    },
    icon: "⚠️",    
    autoClose: 5000,               // Auto-close after 5 seconds
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,                // Optional custom icon
  });
    
  };
  // const handleButtonClick = () => {
  //   if (showDuplicate1 && duplicate.length > 0) {
  //     setShowDuplicate1(false);
  //     setwaitForSubmission(false);
  //   } else {
  //     // Proceed with adding the candidate
  //     confirmAddCandidate();
  //   }
  // };

  return (
    <section
      className=" min-vh-100 d-flex align-items-center"
      style={{
        background: 'linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 1) 100%)',
        // paddingTop: '100px'
      }}
    >
      <div className="container">
        {/* Header Section */}
        <div className="row mb-5">
          <div className="col-12 text-center">
            <h1
              className="display-4 fw-bold text-white mb-2"
              style={{
                textShadow: '2px 2px 4px rgba(0,0,0,0.3)',
                letterSpacing: '-0.02em'
              }}
            >
              Current Openings
            </h1>
            <p className="lead text-white-50 mb-0">
              Join our team and build the future together
            </p>
          </div>
        </div>

        {/* Jobs Table Section */}
        <div className="row">
          <div className="col-12">
            <div
              className="card shadow-lg border-0"
              style={{
                borderRadius: '20px',
                overflow: 'hidden',
                backdropFilter: 'blur(10px)',
                background: 'rgba(255, 255, 255, 0.98)'
              }}
            >
              <div className="card-body p-0">
                <div
                  className="table-responsive"
                  style={{
                    maxHeight: '500px',
                    overflowY: 'auto'
                  }}
                >
                  <table className="table table-hover mb-0">
                    <thead
                      className="sticky-top"
                      style={{
                        backgroundColor: '#f8f9fa',
                        borderBottom: '2px solid #dee2e6'
                      }}
                    >
                      <tr>
                        <th className="py-4 px-4 fw-bold text-dark border-0" style={{ fontSize: '1.1rem' }}>Role</th>
                        <th className="py-4 px-4 fw-bold text-dark border-0" style={{ fontSize: '1.1rem' }}>Mode</th>
                        <th className="py-4 px-4 fw-bold text-dark border-0" style={{ fontSize: '1.1rem' }}>Contract Type</th>
                        <th className="py-4 px-4 fw-bold text-dark border-0" style={{ fontSize: '1.1rem' }}>Location</th>
                        <th className="py-4 px-4 fw-bold text-dark border-0 text-center" style={{ fontSize: '1.1rem' }}>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {jobPosts.map((job) => (
                        <React.Fragment key={job.jobid}>
                          <tr
                            className="border-0"
                            style={{
                              transition: 'all 0.3s ease',
                              cursor: 'pointer'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.backgroundColor = '#f8f9fa';
                              e.currentTarget.style.transform = 'translateY(-2px)';
                              e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.backgroundColor = '';
                              e.currentTarget.style.transform = '';
                              e.currentTarget.style.boxShadow = '';
                            }}
                          >
                            <td className="py-4 px-4 border-0" data-label="Role">
                              <div className="fw-semibold text-dark" style={{ fontSize: '1rem' }}>
                                {job.jobtitle}
                              </div>
                            </td>
                            <td className="py-4 px-4 border-0" data-label="Mode">
                              <span
                                  onClick={checktoast}
                                className="badge rounded-pill px-3 py-2"
                                style={{
                                  backgroundColor: job.mode === 'Remote' ? '#28a745' : job.mode === 'Hybrid' ? '#ffc107' : '#007bff',
                                  color: job.mode === 'Hybrid' ? '#000' : '#fff',
                                  fontSize: '0.85rem'
                                }}
                              >
                                {job.mode}
                              </span>
                            </td>
                            <td className="py-4 px-4 border-0" data-label="Contract Type">
                              <span className="text-muted">Full time</span>
                            </td>
                            <td className="py-4 px-4 border-0" data-label="Location">
                              <div className="d-flex align-items-center">
                                <FaMapMarkerAlt className="text-muted me-2" size={14} />
                                <span className="text-dark">{job.loc}</span>
                              </div>
                            </td>
                            <td className="py-4 px-4 border-0 text-center" data-label="Actions">
                              <div className="d-flex gap-2 justify-content-center">
                                <button
                                  className="btn btn-outline-primary btn-sm px-3 py-2 d-flex align-items-center gap-2"
                                  onClick={() => openModal(job)}
                                  style={{
                                    borderRadius: '8px',
                                    transition: 'all 0.3s ease',
                                    fontSize: '0.875rem',
                                    fontWeight: '500'
                                  }}
                                  onMouseEnter={(e) => {
                                    e.target.style.transform = 'translateY(-1px)';
                                    e.target.style.boxShadow = '0 4px 8px rgba(0,123,255,0.3)';
                                  }}
                                  onMouseLeave={(e) => {
                                    e.target.style.transform = '';
                                    e.target.style.boxShadow = '';
                                  }}
                                >
                                  <FaEye size={14} />
                                  View JD
                                </button>

                                <button
                                  className="btn btn-sm px-3 py-2 d-flex align-items-center gap-2 text-white"
                                  onClick={() => openapplyModal(job)}
                                  style={{
                                    backgroundColor: '#002956',
                                    border: 'none',
                                    borderRadius: '8px',
                                    transition: 'all 0.3s ease',
                                    fontSize: '0.875rem',
                                    fontWeight: '500',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    gap: '0.5rem'
                                  }}
                                  onMouseEnter={(e) => {
                                    e.target.style.backgroundColor = '#00a0e9';
                                    e.target.style.transform = 'translateY(-1px)';
                                    e.target.style.boxShadow = '0 4px 8px rgba(0,160,233,0.4)';
                                  }}
                                  onMouseLeave={(e) => {
                                    e.target.style.backgroundColor = '#002956';
                                    e.target.style.transform = '';
                                    e.target.style.boxShadow = '';
                                  }}
                                >
                                 <span> <FaPaperPlane size={10} /></span>
                                  Apply
                                </button>
                              </div>
                            </td>
                          </tr>

                        </React.Fragment>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* JD Modal */}
      <Modal
        isOpen={!!selectedJob}
        onRequestClose={closeModal}
        contentLabel="Job Description Modal"
        style={{
          overlay: {
            backgroundColor: "rgba(0, 0, 0, 0.7)",
            backdropFilter: "blur(5px)",
            zIndex: 9999,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          },
          content: {
            position: 'relative',
            top: 'auto',
            left: 'auto',
            right: 'auto',
            bottom: 'auto',
            width: window.innerWidth <= 768 ? '95%' : '70%',
            maxWidth: '900px',
            maxHeight: '85vh',
            overflow: 'hidden',
            margin: 'auto',
            background: 'white',
            borderRadius: '20px',
            border: 'none',
            padding: '0',
            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
          },
        }}
      >
        {selectedJob && (
          <div className="h-100 d-flex flex-column">
            {/* Modal Header */}
            <div
              className="d-flex justify-content-between align-items-center p-4 border-bottom"
              style={{
                background: ' #002956 ',
                borderRadius: '20px 20px 0 0'
              }}
            >
              <h2 className="text-white mb-0 fw-bold" style={{ fontSize: '1.5rem' }}>
                Job Description
              </h2>
              <div className="d-flex align-items-center gap-3">
                <button
                  className="btn btn-light btn-sm d-flex align-items-center gap-2 px-3 py-2"
                  onClick={() => downloadJD(
                    selectedJob.jd_pdf_base64,
                    selectedJob.downloadFileName,
                    selectedJob.jd_pdf_extension
                  )}
                  style={{
                    borderRadius: '10px',
                    transition: 'all 0.3s ease',
                    fontWeight: '500'
                  }}
                >
                  <FaDownload size={14} />
                  Download
                </button>
                <button
                  className="btn btn-light btn-sm p-2"
                  onClick={closeModal}
                  style={{
                    borderRadius: '10px',
                    width: '40px',
                    height: '40px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <FaTimes size={16} />
                </button>
              </div>
            </div>

            {/* Modal Body */}
            <div
              className="flex-grow-1 p-4"
              style={{
                overflowY: 'auto',
                maxHeight: 'calc(85vh - 100px)'
              }}
            >
              <div
                className="job-description-content"
                dangerouslySetInnerHTML={{ __html: selectedJob.jd_html }}
                style={{
                  lineHeight: '1.6',
                  color: '#333',
                  fontSize: '1rem'
                }}
              />
              <style jsx>{`
                .job-description-content h3 {
                  color: #002956 !important;
                  font-weight: 600 !important;
                  font-size: 1.2rem !important;
                  margin: 1.5rem 0 0.8rem 0 !important;
                  border-bottom: 2px solid #00a0e9 !important;
                  padding-bottom: 0.5rem !important;
                }
                .job-description-content ul {
                  padding-left: 1.5rem !important;
                  margin-bottom: 1rem !important;
                }
                .job-description-content li {
                  margin-bottom: 0.5rem !important;
                  line-height: 1.5 !important;
                }
                .job-description-content p {
                  margin: 0.8rem 0 !important;
                  line-height: 1.6 !important;
                }
              `}</style>
            </div>
          </div>
        )}
      </Modal>

      {/* Apply Modal */}
      <Modal
        isOpen={!!applyJob}
        onRequestClose={closeapplyModal}
        contentLabel="Apply Modal"
        style={{
          overlay: {
            backgroundColor: "rgba(0, 0, 0, 0.7)",
            backdropFilter: "blur(5px)",
            zIndex: 9999,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          },
          content: {
            position: 'relative',
            top: 'auto',
            left: 'auto',
            right: 'auto',
            bottom: 'auto',
            width: window.innerWidth <= 768 ? '95%' : '500px',
            maxHeight: '85vh',
            overflow: 'hidden',
            margin: 'auto',
            background: 'white',
            borderRadius: '20px',
            border: 'none',
            padding: '0',
            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
          },
        }}
      >
        {applyJob && (
          <div className="h-100 d-flex flex-column">
            {/* Modal Header */}
            <div
              className="d-flex justify-content-between align-items-center p-4 border-bottom"
              style={{
                background: ' #002956 ',
                borderRadius: '20px 20px 0 0'
              }}
            >
              <h2 className="text-white mb-0 fw-bold" style={{ fontSize: '1.3rem' }}>
                {applyJob.jobtitle}
              </h2>
              <button
                className="btn btn-light btn-sm p-2"
                onClick={closeapplyModal}
                style={{
                  borderRadius: '10px',
                  width: '40px',
                  height: '40px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <FaTimes size={16} />
              </button>
            </div>

            {/* Modal Body */}
            <div className="p-4">
              <div className="row g-3 mb-4">
                <div className="col-md-6">
                  <div className="d-flex align-items-center gap-2 p-3 bg-light rounded-3">
                    <FaClock className="text-primary" size={16} />
                    <div>
                      <small className="text-muted d-block" >Experience</small>
                      <span className="fw-semibold" style={{ color: "#333333" }}>{applyJob.min} - {applyJob.max} years</span>
                    </div>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="d-flex align-items-center gap-2 p-3 bg-light rounded-3">
                    <FaMapMarkerAlt className="text-primary" size={16} />
                    <div>
                      <small className="text-muted d-block">Location</small>
                      <span className="fw-semibold" style={{ color: "#333333" }}>{applyJob.loc}</span>
                    </div>
                  </div>
                </div>
                <div className="col-12">
                  <div className="d-flex align-items-start gap-2 p-3 bg-light rounded-3">
                    <FaUser className="text-primary mt-1" size={16} />
                    <div className="flex-grow-1">
                      <small className="text-muted d-block">Skills Required</small>
                      <span className="fw-semibold" style={{ color: "#333333" }}>{applyJob.skills}</span>
                    </div>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="d-flex align-items-center gap-2 p-3 bg-light rounded-3">
                    <FaBuilding className="text-primary" size={16} />
                    <div>
                      <small className="text-muted d-block">Work Mode</small>
                      <span className="fw-semibold" style={{ color: "#333333" }}>{applyJob.mode}</span>
                    </div>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="d-flex align-items-center gap-2 p-3 bg-light rounded-3">
                    <FaClock className="text-primary" size={16} />
                    <div>
                      <small className="text-muted d-block">Shift Timings</small>
                      <span className="fw-semibold" style={{ color: "#333333" }}>{applyJob.qal}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="text-center">
                <button
                  className="btn btn-lg px-5 py-3 text-white fw-semibold"
                  onClick={() => handleOpenModal(applyJob)}
                  style={{
                    backgroundColor: '#002956',
                    border: 'none',
                    borderRadius: '15px',
                    transition: 'all 0.3s ease',
                    fontSize: '1.1rem',
                    boxShadow: '0 4px 15px rgba(0, 41, 86, 0.3)'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.backgroundColor = '#00a0e9';
                    e.target.style.transform = 'translateY(-2px)';
                    e.target.style.boxShadow = '0 6px 20px rgba(0, 160, 233, 0.4)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.backgroundColor = '#002956';
                    e.target.style.transform = '';
                    e.target.style.boxShadow = '0 4px 15px rgba(0, 41, 86, 0.3)';
                  }}
                >
                  <FaPaperPlane className="me-2" size={16} />
                  Apply Now
                </button>
              </div>
            </div>
          </div>
        )}
      </Modal>
      {/* Application Form Modal */}
      <Modal
        isOpen={applyform}
        onRequestClose={handleCloseModal}
        contentLabel="Application Form Modal"
        style={{
          overlay: {
            backgroundColor: "rgba(0, 0, 0, 0.2)",
            backdropFilter: "blur(5px)",
            zIndex: 9999,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          },
          content: {
            position: 'relative',
            top: 'auto',
            left: 'auto',
            right: 'auto',
            bottom: 'auto',
            width: window.innerWidth <= 768 ? '95%' : '90%',
            maxWidth: '950px',
            maxHeight: '90vh',
            height: "100vh",
            overflow: 'hidden',
            margin: 'auto',
            background: 'white',
            borderRadius: '20px',
            border: 'none',
            padding: '0',
            boxShadow: '0 25px 80px rgba(0, 0, 0, 0.4)',
          },
        }}
      >
        <div className="h-100 d-flex flex-column">
          {/* Modal Header */}
          <div
            className="d-flex justify-content-between align-items-center p-4 border-bottom"
            style={{
              background: ' #002956 ',
              borderRadius: '20px 20px 0 0'
            }}
          >
            <h2 className="text-white mb-0 fw-bold" style={{ fontSize: '1.5rem' }}>
              Profile Submission
            </h2>
            <button
              className="btn btn-light btn-sm p-2"
              onClick={handleCloseModal}
              style={{
                borderRadius: '10px',
                width: '40px',
                height: '40px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <FaTimes size={16} />
            </button>
          </div>

          {/* Modal Body */}
          <div
            className="flex-grow-1 p-4"
            style={{
              overflowY: 'auto',
              maxHeight: '90vh',
            }}
          >
            <form className="needs-validation" noValidate  >
              <div
                className="row g-4"
                style={{
                  maxHeight: 'calc(90vh - 200px)',
                  overflowY: 'auto',
                  paddingRight: '10px'
                }}
              >
                {/* Personal Information Section */}
                <div className="col-12">
                  <h5 className="text-primary fw-bold mb-3 d-flex align-items-center gap-2">
                    <FaUser size={18} />
                    Personal Information
                  </h5>
                </div>

                   {/* Resume Upload */}
                <div className="col-md-6" style={{ marginTop: "12px" }}>

                  <label htmlFor="resume" style={{ color: "#333333" }}>Upload Resume * {isLoading && <FaSpinner className="fa-spin ms-2" />}</label>
                  <input
                    type="file"
                    className="form-control"
                    id="resume"
                    name="resume"
                    accept=".pdf,.doc,.docx"
                    onChange={handleSubmitResume}
                    required
                    style={{
                      borderRadius: '10px',
                      paddingTop: '12px'
                    }}
                  />


                </div>

                {/* Name */}
                <div className="col-md-6">
                  <div className="form-floating">
                    <input
                      type="text"
                      className="form-control"
                      id="name"
                      name="name"
                      placeholder="Full Name"
                      value={candidate_details.name}
                      onChange={handleChange}
                      required
                      style={{ borderRadius: '10px' }}
                    />
                    <label htmlFor="name">Full Name *</label>
                  </div>
                </div>

                {/* Email */}
                <div className="col-md-6">
                  <div className="form-floating">
                    <input
                      type="email"
                      className="form-control"
                      id="email"
                      name="email"
                      placeholder="Email Address"
                      value={candidate_details.email}
                      onChange={handleChange}
                      required
                      style={{ borderRadius: '10px' }}
                    />
                    <label htmlFor="email">Email Address *</label>
                  </div>
                </div>

                {/* Phone */}
                <div className="col-md-6">
                  <div className="form-floating">
                    <input
                      type="tel"
                      className="form-control"
                      id="mobile"
                      name="mobile"
                      placeholder="Phone Number"
                      value={candidate_details.mobile}
                      onChange={handleChange}
                      required
                      style={{ borderRadius: '10px' }}
                    />
                    <label htmlFor="mobile">Phone Number *</label>
                  </div>
                </div>

                {/* Skills */}
                <div className="col-md-6">
                  <div className="form-floating">
                    <input
                      type="text"
                      className="form-control"
                      id="skills"
                      name="skills"
                      placeholder="Skills"
                      value={candidate_details.skills}
                      onChange={handleChange}
                      required
                      style={{ borderRadius: '10px' }}
                    />
                    <label htmlFor="skills">Skills *</label>
                  </div>
                </div>

                {/* Qualifications */}
                <div className="col-md-6">
                  <div className="form-floating">
                    <input
                      type="text"
                      className="form-control"
                      id="qualifications"
                      name="qualifications"
                      placeholder="Qualifications"
                      value={candidate_details.qualifications}
                      onChange={handleChange}
                      required
                      style={{ borderRadius: '10px' }}
                    />
                    <label htmlFor="qualifications">Qualifications *</label>
                  </div>
                </div>


                {/* Professional Information Section */}
                <div className="col-12 mt-3">
                  <h5 className="text-primary fw-bold mb-3 d-flex align-items-center gap-2">
                    <FaBuilding size={18} />
                    Professional Information
                  </h5>
                </div>

                {/* Current Company */}
                <div className="col-md-6" >
                  <div className="form-floating">
                    <input
                      type="text"
                      className="form-control"
                      id="current_company"
                      name="current_company"
                      placeholder="Current Company"
                      value={candidate_details.current_company}
                      onChange={handleChange}
                      required
                      style={{ borderRadius: '10px' }}
                    />
                    <label htmlFor="current_company">Current Company *</label>
                  </div>
                </div>

                {/* Current Position */}
                <div className="col-md-6" >
                  <div className="form-floating">
                    <input
                      type="text"
                      className="form-control"
                      id="position"
                      name="position"
                      placeholder="Current Position"
                      value={candidate_details.position}
                      onChange={handleChange}
                      required
                      style={{ borderRadius: '10px' }}
                    />
                    <label htmlFor="position">Current Position *</label>
                  </div>
                </div>

                {/* Current Job Location */}
                <div className="col-md-6" >
                  <div className="form-floating">
                    <input
                      type="text"
                      className="form-control"
                      id="current_job_location"
                      name="current_job_location"
                      placeholder="Current Job Location"
                      value={candidate_details.current_job_location}
                      onChange={handleChange}
                      required
                      style={{ borderRadius: '10px' }}
                    />
                    <label htmlFor="current_job_location">Current Job Location *</label>
                  </div>
                </div>

                {/* Preferred Job Location */}
                <div className="col-md-6" >
                  <div className="form-floating">
                    <input
                      type="text"
                      className="form-control"
                      id="preferred_job_location"
                      name="preferred_job_location"
                      placeholder="Preferred Job Location"
                      value={candidate_details.preferred_job_location}
                      onChange={handleChange}
                      required
                      style={{ borderRadius: '10px' }}
                    />
                    <label htmlFor="preferred_job_location">Preferred Job Location *</label>
                  </div>
                </div>

                {/* Reason for Job Change */}
                <div className="col-12" >
                  <div className="form-floating">
                    <textarea
                      className="form-control"
                      id="reason_for_job_change"
                      name="reason_for_job_change"
                      placeholder="Reason for Job Change"
                      value={candidate_details.reason_for_job_change}
                      onChange={handleChange}
                      style={{
                        borderRadius: '10px',
                        minHeight: '100px'
                      }}
                    />
                    <label htmlFor="reason_for_job_change">Reason for Job Change</label>
                  </div>
                </div>
                {/* Experience Section */}
                <div className="col-12 mt-3">
                  <h5 className="text-primary fw-bold mb-3 d-flex align-items-center gap-2">
                    <FaClock size={18} />
                    Experience Details
                  </h5>
                </div>

                {/* Total Experience */}
                <div className="col-md-6" style={{ marginTop: "5px" }}>
                  <label className="form-label fw-semibold" style={{ color: "#333333" }}>Total Experience *</label>
                  <div className="row g-2">
                    <div className="col-6">
                      <select
                        className="form-select"
                        name="total_experience_years"
                        value={candidate_details.total_experience_years}
                        onChange={handleChange}
                        required
                        style={{ borderRadius: '10px' }}
                      >
                        <option value="">Years</option>
                        {Array.from({ length: 21 }, (_, i) => (
                          <option key={i} value={i}>{i}</option>
                        ))}
                      </select>
                    </div>
                    <div className="col-6">
                      <select
                        className="form-select"
                        name="total_experience_months"
                        value={candidate_details.total_experience_months}
                        onChange={handleChange}
                        required
                        style={{ borderRadius: '10px' }}
                      >
                        <option value="">Months</option>
                        {Array.from({ length: 12 }, (_, i) => (
                          <option key={i} value={i}>{i}</option>
                        ))}
                      </select>
                    </div>
                  </div>
                </div>

                {/* Relevant Experience */}
                <div className="col-md-6" style={{ marginTop: "5px" }}>
                  <label className="form-label fw-semibold" style={{ color: "#333333" }}>Relevant Experience *</label>
                  <div className="row g-2">
                    <div className="col-6">
                      <select
                        className="form-select"
                        name="relevant_experience_years"
                        value={candidate_details.relevant_experience_years}
                        onChange={handleChange}
                        required
                        style={{ borderRadius: '10px' }}
                      >
                        <option value="">Years</option>
                        {Array.from({ length: 21 }, (_, i) => (
                          <option key={i} value={i}>{i}</option>
                        ))}
                      </select>
                    </div>
                    <div className="col-6">
                      <select
                        className="form-select"
                        name="relevant_experience_months"
                        value={candidate_details.relevant_experience_months}
                        onChange={handleChange}
                        required
                        style={{ borderRadius: '10px' }}
                      >
                        <option value="">Months</option>
                        {Array.from({ length: 12 }, (_, i) => (
                          <option key={i} value={i}>{i}</option>
                        ))}
                      </select>
                    </div>
                  </div>
                </div>
                {/* Compensation Section */}
                <div className="col-12 mt-3">
                  <h5 className="text-primary fw-bold mb-3 d-flex align-items-center gap-2">
                    <FaMoneyBillWave size={18} />
                    Compensation Details
                  </h5>
                </div>

                {/* Current CTC */}
                <div className="col-md-6" style={{ marginTop: "5px" }}>
                  <label className="form-label fw-semibold" style={{ color: "#333333" }}>Current CTC *</label>
                  <div className="input-group">
                    <select
                      className="form-select"
                      name="current_ctc_type"
                      value={candidate_details.current_ctc_type}
                      onChange={handleChange}
                      required
                      style={{
                        borderRadius: '10px 0 0 10px',
                        maxWidth: '120px'
                      }}
                    >
                      <option value="INR">INR (LPA)</option>
                      <option value="USD">USD</option>
                      <option value="EUR">EUR</option>
                      <option value="CAD">CAD</option>
                    </select>
                    <input
                      type="number"
                      className="form-control"
                      name="current_ctc_value"
                      value={candidate_details.current_ctc_value}
                      onChange={handleChange}
                      min="0"
                      step="0.1"
                      required
                      style={{ borderRadius: '0 10px 10px 0' }}
                      placeholder="Enter amount"
                    />
                  </div>
                </div>

                {/* Expected CTC */}
                <div className="col-md-6" style={{ marginTop: "5px" }}>
                  <label className="form-label fw-semibold" style={{ color: "#333333" }}>Expected CTC *</label>
                  <div className="input-group">
                    <select
                      className="form-select"
                      name="expected_ctc_type"
                      value={candidate_details.expected_ctc_type}
                      onChange={handleChange}
                      required
                      style={{
                        borderRadius: '10px 0 0 10px',
                        maxWidth: '120px'
                      }}
                    >
                      <option value="INR">INR (LPA)</option>
                      <option value="USD">USD</option>
                      <option value="EUR">EUR</option>
                      <option value="CAD">CAD</option>
                    </select>
                    <input
                      type="number"
                      className="form-control"
                      name="expected_ctc_value"
                      value={candidate_details.expected_ctc_value}
                      onChange={handleChange}
                      min="0"
                      step="0.1"
                      required
                      style={{ borderRadius: '0 10px 10px 0' }}
                      placeholder="Enter amount"
                    />
                  </div>
                </div>

                {/* Notice Period Section */}
                <div className="col-12 mt-3">
                  <h5 className="text-primary fw-bold mb-3">Notice Period Details</h5>
                </div>

                {/* Serving Notice Period */}
                <div className="col-md-6" style={{ marginTop: "5px" }}>
                  <div className="form-floating">
                    <select
                      className="form-select"
                      id="serving_notice_period"
                      name="serving_notice_period"
                      value={candidate_details.serving_notice_period}
                      onChange={handleChange}
                      required
                      style={{ borderRadius: '10px' }}
                    >
                      <option value="">Select</option>
                      <option value="yes">Yes</option>
                      <option value="no">No</option>
                      <option value="completed">Completed</option>
                    </select>
                    <label htmlFor="serving_notice_period">Serving Notice Period *</label>
                  </div>
                </div>

                {/* Conditional Notice Period Fields */}
                {candidate_details.serving_notice_period === "yes" && (
                  <>
                    <div className="col-md-6">
                      <div className="form-floating">
                        <input
                          type="date"
                          className="form-control"
                          id="last_working_date"
                          name="last_working_date"
                          value={candidate_details.last_working_date}
                          onChange={handleChange}
                          required
                          style={{ borderRadius: '10px' }}
                        />
                        <label htmlFor="last_working_date">Last Working Date *</label>
                      </div>
                    </div>
                    <div className="col-md-6">
                      <div className="form-check mt-3">
                        <input
                          className="form-check-input"
                          type="checkbox"
                          id="buyout"
                          name="buyout"
                          checked={candidate_details.buyout}
                          onChange={handleChange}
                        />
                        <label className="form-check-label fw-semibold" htmlFor="buyout">
                          Buyout Available
                        </label>
                      </div>
                    </div>
                  </>
                )}

                {candidate_details.serving_notice_period === "no" && (
                  <>
                    <div className="col-md-6">
                      <div className="form-floating">
                        <select
                          className="form-select"
                          id="period_of_notice"
                          name="period_of_notice"
                          value={candidate_details.period_of_notice}
                          onChange={handleChange}
                          required
                          style={{ borderRadius: '10px' }}
                        >
                          <option value="">Select Notice Period</option>
                          <option value="0-15 days">0-15 days</option>
                          <option value="1 Month">1 Month</option>
                          <option value="2 Months">2 Months</option>
                          <option value="3 Months">3 Months</option>
                          <option value="More than 3 Months">More than 3 Months</option>
                        </select>
                        <label htmlFor="period_of_notice">Notice Period *</label>
                      </div>
                    </div>
                    <div className="col-md-6">
                      <div className="form-check mt-3">
                        <input
                          className="form-check-input"
                          type="checkbox"
                          id="buyout_no"
                          name="buyout"
                          checked={candidate_details.buyout}
                          onChange={handleChange}
                        />
                        <label className="form-check-label fw-semibold" htmlFor="buyout_no">
                          Buyout Available
                        </label>
                      </div>
                    </div>
                  </>
                )}
                {/* Offer Details Section */}
                <div className="col-md-6" style={{ marginTop: "5px" }}>
                  <div className="form-floating">
                    <select
                      className="form-select"
                      id="holding_offer"
                      name="holding_offer"
                      value={candidate_details.holding_offer}
                      onChange={handleChange}
                      style={{ borderRadius: '10px' }}
                    >
                      <option value="">Select</option>
                      <option value="yes">Yes</option>
                      <option value="no">No</option>
                      <option value="pipeline">Pipeline</option>
                    </select>
                    <label htmlFor="holding_offer">Holding Offer</label>
                  </div>
                </div>

                {/* Conditional Offer Fields */}
                {candidate_details.holding_offer === "yes" && (
                  <>
                    <div className="col-md-6">
                      <div className="form-floating">
                        <input
                          type="number"
                          className="form-control"
                          id="total_offers"
                          name="total_offers"
                          value={candidate_details.total_offers}
                          onChange={(e) => {
                            const value = parseInt(e.target.value, 10);
                            if (value >= 0 || e.target.value === "") {
                              handleChange(e);
                            }
                          }}
                          required
                          style={{ borderRadius: '10px' }}
                          placeholder="Number of offers"
                        />
                        <label htmlFor="total_offers">Total Offers *</label>
                      </div>
                    </div>
                    <div className="col-md-6">
                      <div className="form-floating">
                        <input
                          type="number"
                          className="form-control"
                          id="highest_package"
                          name="highest_package"
                          value={candidate_details.highest_package}
                          onChange={(e) => {
                            const value = parseFloat(e.target.value);
                            if (value >= 0 || e.target.value === "") {
                              handleChange(e);
                            }
                          }}
                          required
                          step="0.1"
                          style={{ borderRadius: '10px' }}
                          placeholder="Highest package"
                        />
                        <label htmlFor="highest_package">Highest Package (LPA) *</label>
                      </div>
                    </div>
                  </>
                )}

                {/* Additional Information Section */}
                <div className="col-12 mt-3">
                  <h5 className="text-primary fw-bold mb-3">Additional Information</h5>
                </div>

                {/* LinkedIn URL */}
                <div className="col-md-6" >
                  <div className="form-floating">
                    <input
                      type="url"
                      className="form-control"
                      id="linkedin"
                      name="linkedin"
                      value={candidate_details.linkedin}
                      onChange={handleChange}
                      style={{ borderRadius: '10px' }}
                      placeholder="LinkedIn URL"
                    />
                    <label htmlFor="linkedin">LinkedIn URL</label>
                  </div>
                </div>

                {/* Remarks */}
                <div className="col-12" >
                  <div className="form-floating">
                    <textarea
                      className="form-control"
                      id="remarks"
                      name="remarks"
                      value={candidate_details.remarks}
                      onChange={handleChange}
                      style={{
                        borderRadius: '10px',
                        minHeight: '120px'
                      }}
                      placeholder="Additional remarks"
                    />
                    <label htmlFor="remarks">Remarks</label>
                  </div>
                </div>

                {/* Submit Button */}

              </div>
              <div className="col-12 text-center mt-4">
                <button
                  type="submit"
                  className="btn btn-lg px-5 py-3 text-white fw-semibold"
                  disabled={waitForSubmission}
                  onClick={handleSubmit}
                  style={{
                    backgroundColor: '#002956',
                    border: 'none',
                    borderRadius: '15px',
                    transition: 'all 0.3s ease',
                    fontSize: '1.1rem',
                    boxShadow: '0 4px 15px rgba(0, 41, 86, 0.3)',
                    minWidth: '200px'
                  }}
                  onMouseEnter={(e) => {
                    if (!waitForSubmission) {
                      e.target.style.backgroundColor = '#00a0e9';
                      e.target.style.transform = 'translateY(-2px)';
                      e.target.style.boxShadow = '0 6px 20px rgba(0, 160, 233, 0.4)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!waitForSubmission) {
                      e.target.style.backgroundColor = '#002956';
                      e.target.style.transform = '';
                      e.target.style.boxShadow = '0 4px 15px rgba(0, 41, 86, 0.3)';
                    }
                  }}
                >
                  {waitForSubmission ? (
                    <>
                      <FaSpinner className="fa-spin me-2" />
                      Submitting...
                    </>
                  ) : (
                    <>
                      <FaPaperPlane className="me-2" />
                      Submit
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </Modal>

      {/* Global Styles for Enhanced UI */}
      <style jsx global>{`
        /* Custom scrollbar for form */
        .form-control:focus,
        .form-select:focus {
          border-color: #00a0e9 !important;
          box-shadow: 0 0 0 0.2rem rgba(0, 160, 233, 0.25) !important;
        }

        /* Smooth transitions for all interactive elements */
        .btn, .form-control, .form-select, .card {
          transition: all 0.3s ease !important;
        }

        /* Enhanced table responsiveness */
        @media (max-width: 768px) {
          .table-responsive table,
          .table-responsive thead,
          .table-responsive tbody,
          .table-responsive th,
          .table-responsive td,
          .table-responsive tr {
            display: block !important;
          }

          .table-responsive thead tr {
            position: absolute !important;
            top: -9999px !important;
            left: -9999px !important;
          }

          .table-responsive tr {
            border: 1px solid #ccc !important;
            margin-bottom: 10px !important;
            padding: 15px !important;
            border-radius: 10px !important;
            background: white !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
          }

          .table-responsive td {
            border: none !important;
            position: relative !important;
            padding-left: 50% !important;
            padding-top: 10px !important;
            padding-bottom: 10px !important;
          }

          .table-responsive td:before {
            content: attr(data-label) !important;
            position: absolute !important;
            left: 15px !important;
            width: 45% !important;
            padding-right: 10px !important;
            white-space: nowrap !important;
            font-weight: bold !important;
            color: #002956 !important;
          }
        }

        /* Loading animation */
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .fa-spin {
          animation: spin 1s linear infinite !important;
        }

        /* Form validation styles */
        .was-validated .form-control:valid,
        .was-validated .form-select:valid {
          border-color: #28a745 !important;
        }

        .was-validated .form-control:invalid,
        .was-validated .form-select:invalid {
          border-color: #dc3545 !important;
        }
      `}</style>
    </section>
  );
};

export default Careers;