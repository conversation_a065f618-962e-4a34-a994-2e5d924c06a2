import React, { useState, useEffect } from "react";
import { FaLinkedin, FaEnvelope } from "react-icons/fa";
 
const TeamPage = () => {
  const teamMembers = [
    {
      name: "<PERSON>",
      role: "Founder & CEO",
      description:
        "Serial Entrepreneur, over 18 years of experience managing technology business.",
      linkedin: "linkedin.com/in/krishna-samanth-bb0a46b5/",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
    {
      name: "<PERSON><PERSON> <PERSON>",
      role: "COO & Co-Founder",
      description:
        "Over 30 years of experience in Global Software Delivery, Vision, Strategy and Operational Excellence.",
      linkedin: "linkedin.com/in/durga-prasad-a-632ab61a1/",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
    {
      name: "<PERSON><PERSON>",
      role: "CHRO & Co-Founder - Dubai",
      description:
        "30 years in Global HR functions for IT (Products / Projects) and Retail sectors.",
      linkedin: "linkedin.com/in/sc007/",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
    {
      name: "V<PERSON>hree",
      role: "CTO & Managing Partner - Australia",
      description:
        "20 years in IT Development and Enterprise Delivery across Finance, Banking, Telecom, and Healthcare.",
      linkedin: "linkedin.com/in/vamshree/",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
    {
      name: "Sreeni Raju",
      role: "Head – ERP Software Solutions & Delivery - USA",
      description:
        "22 Years of experience in core HR, global ERP implementations and consulting in some of the global fortune 500 companies.",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
    {
      name: "Nishant Seth",
      role: "Practice Head- Data, AI/ML and Analytics",
      description:
        "Data Analytics consultant with 25+ years in AI/ML, data lakes, and analytics across industries.",
      linkedin: "linkedin.com/in/nishantseth/",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
    {
      name: "Kishore Seetharam",
      role: "Head - Talent Acquisition",
      description: "Over 17 Years of Experience in Technical Recruitment.",
      linkedin: "linkedin.com/in/seetharam-kishore-25a9881a/",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
    {
      name: "Dr. Mehala N",
      role: "Advisor",
      description:
        "20+ years in academia, teaching, content development, and research in Data Science & Machine Intelligence.",
      linkedin: "linkedin.com/in/dr-mehala-n-********/",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
    {
      name: "Vinesh Singh",
      role: "Head - L&D",
      description:
        "Over 35 years in Finance, Accounting, strategic planning, Budgeting, Joint ventures and Vendor management",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
    {
      name: "Sai Krishna",
      role: "Head - Product & Innovation",
      description:
        "Over 13+ years of experience managing research and development in IoT",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
  ];
 
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
 
  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);
 
  // Responsive breakpoints
  const isDesktop = windowWidth >= 1024;
  const isTablet = windowWidth >= 600 && windowWidth < 1024;
 
  // Grid layout logic
  const cardsPerRow = isDesktop ? 4 : isTablet ? 2 : 1;
 
  // Create rows with given cardsPerRow
  const rows = [];
  for (let i = 0; i < teamMembers.length; i += cardsPerRow) {
    rows.push(teamMembers.slice(i, i + cardsPerRow));
  }
 
  const cardWidth = 260;
  const cardHeight = 460; // Increased height for better icon placement
 
  const Card = ({ member }) => (
    <div
      style={{
        background: "#1e2a47",
        borderRadius: 16,
        padding: "1.8rem",
        boxShadow:
          "0 4px 12px rgba(0,0,0,0.25), inset 0 0 15px rgba(255,255,255,0.05)",
        display: "flex",
        flexDirection: "column",
        transition: "transform 0.3s ease, box-shadow 0.3s ease",
        cursor: "default",
        width: cardWidth,
        height: cardHeight,
        boxSizing: "border-box",
        overflow: "hidden",
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = "scale(1.05)";
        e.currentTarget.style.boxShadow =
          "0 10px 30px rgba(0,0,0,0.5), inset 0 0 20px rgba(255,255,255,0.1)";
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = "scale(1)";
        e.currentTarget.style.boxShadow =
          "0 4px 12px rgba(0,0,0,0.25), inset 0 0 15px rgba(255,255,255,0.05)";
      }}
    >
      <img
        src={member.image}
        alt={member.name}
        style={{
          width: 130,
          height: 130,
          borderRadius: "50%",
          objectFit: "cover",
          margin: "0 auto 1.3rem",
          border: "3px solid #00a0e9",
          filter: "grayscale(5%) contrast(1.05)",
          transition: "filter 0.3s ease",
          flexShrink: 0,
        }}
      />
      <h5
        style={{
          color: "#00a0e9",
          fontWeight: "700",
          fontSize: "1.25rem",
          letterSpacing: 0.8,
          marginBottom: 6,
          textAlign: "center",
        }}
      >
        {member.name}
      </h5>
      <h6
        style={{
          color: "#cbd5e1",
          fontWeight: "600",
          fontSize: "1rem",
          marginBottom: 12,
          minHeight: "2.2em",
          textAlign: "center",
        }}
      >
        {member.role}
      </h6>
      <p
        style={{
          color: "#d1d9e6",
          fontSize: "0.95rem",
          lineHeight: 1.5,
          fontWeight: "400",
          flexGrow: 1,
          marginBottom: 14,
          textAlign: "center",
        }}
      >
        {member.description}
      </p>
      {(member.linkedin || member.email) && (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            gap: 14,
            marginTop: "auto",
          }}
        >
          {member.linkedin && (
            <a
              href={`https://${member.linkedin}`}
              target="_blank"
              rel="noopener noreferrer"
              aria-label={`LinkedIn profile of ${member.name}`}
              style={iconStyle}
              onMouseEnter={iconHoverIn}
              onMouseLeave={iconHoverOut}
            >
              <FaLinkedin />
            </a>
          )}
          {member.email && (
            <a
              href={`mailto:${member.email}`}
              aria-label={`Send email to ${member.name}`}
              style={iconStyle}
              onMouseEnter={iconHoverIn}
              onMouseLeave={iconHoverOut}
            >
              <FaEnvelope />
            </a>
          )}
        </div>
      )}
    </div>
  );
 
  // Icon styles and hover handlers
  const iconStyle = {
    color: "#00a0e9",
    fontSize: "1.3rem",
    background: "rgba(0, 160, 233, 0.15)",
    padding: 9,
    borderRadius: 10,
    border: "1.3px solid rgba(0, 160, 233, 0.3)",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    width: 36,
    height: 36,
    boxShadow: "0 2px 8px rgba(0, 160, 233, 0.25)",
    transition: "all 0.3s ease",
    cursor: "pointer",
  };
 
  function iconHoverIn(e) {
    e.currentTarget.style.background = "rgba(0, 160, 233, 0.4)";
    e.currentTarget.style.color = "#fff";
    e.currentTarget.style.boxShadow = "0 6px 18px rgba(0, 160, 233, 0.6)";
  }
 
  function iconHoverOut(e) {
    e.currentTarget.style.background = "rgba(0, 160, 233, 0.15)";
    e.currentTarget.style.color = "#00a0e9";
    e.currentTarget.style.boxShadow = "0 2px 8px rgba(0, 160, 233, 0.25)";
  }
 
  // Calculate last row centering style
  const lastRow = rows[rows.length - 1];
  let justifyContent = "start";
  if ((isDesktop && lastRow.length < 4) || (isTablet && lastRow.length < 2)) {
    justifyContent = "center";
  }
 
  return (
    <div
      style={{
        minHeight: "100vh",
        background: "linear-gradient(180deg, #001533 0%, #002a70 100%)",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        padding: "4rem 1.5rem 6rem",
        fontFamily: "'Poppins', sans-serif",
      }}
    >
      <div
        style={{
          maxWidth: 900,
          textAlign: "center",
          marginBottom: "2.5rem",
          padding: "0 1rem",
        }}
      >
        <h1
          style={{
            fontSize: "3.6rem",
            fontWeight: "800",
            letterSpacing: "2.6px",
            marginBottom: "1rem",
            background: 'linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            textShadow: '0 0 30px rgba(0, 160, 233, 0.3)'
          }}
        >
          Our Leadership
        </h1>
        <p
          style={{
            color: "#cbd5e1",
            fontSize: "1.2rem",
            lineHeight: "1.9",
            margin: "0 auto",
            fontWeight: 500,
            opacity: 0.9,
          }}
        >
          Since its inception, Makonis has thrived under exceptional leadership — from
          our strong, engaged, and independent board members to our experienced and
          globally distributed senior management team.
        </p>
      </div>
 
      <div
        style={{
          display: "flex",
          flexWrap: "wrap",
          justifyContent,
          gap: 28,
          width: isDesktop
            ? cardWidth * 4 + 28 * 3
            : isTablet
            ? cardWidth * 2 + 28
            : cardWidth,
          maxWidth: "100%",
        }}
      >
        {teamMembers.map((member, idx) => (
          <Card member={member} key={idx} />
        ))}
      </div>
    </div>
  );
};
 
export default TeamPage;