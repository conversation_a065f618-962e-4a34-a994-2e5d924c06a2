import React, { useState, useRef } from 'react';
import { 
  FaPlay, 
  FaPause, 
  FaVolumeUp, 
  FaExpand, 
  FaUsers, 
  FaChartLine, 
  FaClock, 
  FaShieldAlt,
  FaRocket,
  FaCheckCircle,
  FaEnvelope,
  FaPhone,
  FaArrowRight
} from 'react-icons/fa';
import 'bootstrap/dist/css/bootstrap.min.css';

const ATSDemo = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const videoRef = useRef(null);

  const handlePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
    }
  };

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleProgressClick = (e) => {
    if (videoRef.current && duration) {
      const rect = e.currentTarget.getBoundingClientRect();
      const clickX = e.clientX - rect.left;
      const clickRatio = clickX / rect.width;
      const newTime = clickRatio * duration;
      videoRef.current.currentTime = newTime;
    }
  };

  const features = [
    {
      icon: FaUsers,
      title: "Candidate Management",
      description: "Streamline your hiring process with comprehensive candidate tracking and management tools."
    },
    {
      icon: FaChartLine,
      title: "Analytics & Reporting",
      description: "Get detailed insights into your recruitment metrics and make data-driven hiring decisions."
    },
    {
      icon: FaClock,
      title: "Time-to-Hire Optimization",
      description: "Reduce hiring time by 50% with automated workflows and intelligent candidate matching."
    },
    {
      icon: FaShieldAlt,
      title: "Secure & Compliant",
      description: "Enterprise-grade security with GDPR compliance and data protection protocols."
    }
  ];

  const benefits = [
    "Automated resume parsing and candidate scoring",
    "Integrated video interviewing platform",
    "Customizable hiring workflows",
    "Real-time collaboration tools",
    "Mobile-responsive design",
    "API integrations with popular HR tools"
  ];

  return (
    <div className="ats-demo-page">
      <style jsx>{`
        .ats-demo-page {
          overflow-x: hidden;
        }

        .hero-section {
          position: relative;
        }

        .hero-section::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
          opacity: 0.3;
          pointer-events: none;
        }

        .video-container:hover .video-controls {
          opacity: 1;
        }

        .video-controls {
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        .feature-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 8px 25px rgba(0, 43, 89, 0.15);
        }

        .benefits-card {
          position: relative;
          overflow: hidden;
        }

        .benefits-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
          opacity: 0.5;
          pointer-events: none;
        }

        .cta-card:hover {
          transform: translateY(-3px);
          box-shadow: 0 12px 30px rgba(0, 43, 89, 0.2);
        }

        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .hero-section > .container > .row > .col-lg-6 {
          animation: fadeInUp 0.8s ease-out;
        }

        .hero-section > .container > .row > .col-lg-6:nth-child(2) {
          animation-delay: 0.2s;
        }

        .video-section .col-lg-10 {
          animation: fadeInUp 0.8s ease-out 0.4s both;
        }

        .feature-card {
          animation: fadeInUp 0.6s ease-out both;
        }

        .feature-card:nth-child(1) { animation-delay: 0.1s; }
        .feature-card:nth-child(2) { animation-delay: 0.2s; }
        .feature-card:nth-child(3) { animation-delay: 0.3s; }
        .feature-card:nth-child(4) { animation-delay: 0.4s; }

        .progress:hover .progress-bar {
          box-shadow: 0 0 10px rgba(0, 157, 230, 0.5);
        }

        .btn:hover {
          transform: translateY(-2px);
        }

        .demo-stats {
          animation: fadeInUp 0.8s ease-out 0.6s both;
        }
      `}</style>
      {/* Hero Section */}
      <section 
        className="hero-section py-5"
        style={{
          background: 'linear-gradient(135deg, rgba(0, 43, 89, 0.95) 0%, rgba(0, 157, 230, 0.95) 100%)',
          minHeight: '60vh',
          display: 'flex',
          alignItems: 'center'
        }}
      >
        <div className="container">
          <div className="row align-items-center">
            <div className="col-lg-6">
              <h1 
                className="display-4 fw-bold text-white mb-4"
                style={{
                  textShadow: '2px 2px 4px rgba(0,0,0,0.3)',
                  letterSpacing: '-0.02em'
                }}
              >
                Experience Our ATS Platform
              </h1>
              <p className="lead text-white-75 mb-4" style={{ fontSize: '1.2rem', lineHeight: '1.6' }}>
                Watch how our cutting-edge Applicant Tracking System revolutionizes your hiring process. 
                See real features, workflows, and results in action.
              </p>
              <div className="d-flex gap-3">
                <button 
                  className="btn btn-light btn-lg px-4 py-3 fw-semibold"
                  style={{
                    borderRadius: '12px',
                    boxShadow: '0 4px 15px rgba(255, 255, 255, 0.2)'
                  }}
                >
                  <FaRocket className="me-2" />
                  Request Demo
                </button>
                <button 
                  className="btn btn-outline-light btn-lg px-4 py-3 fw-semibold"
                  style={{
                    borderRadius: '12px',
                    borderWidth: '2px'
                  }}
                >
                  Learn More
                </button>
              </div>
            </div>
            <div className="col-lg-6">
              <div className="text-center">
                <div 
                  className="demo-stats p-4 rounded-4"
                  style={{
                    background: 'rgba(255, 255, 255, 0.1)',
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(255, 255, 255, 0.2)'
                  }}
                >
                  <div className="row text-center text-white">
                    <div className="col-4">
                      <h3 className="fw-bold mb-1">50%</h3>
                      <small>Faster Hiring</small>
                    </div>
                    <div className="col-4">
                      <h3 className="fw-bold mb-1">1000+</h3>
                      <small>Companies</small>
                    </div>
                    <div className="col-4">
                      <h3 className="fw-bold mb-1">99.9%</h3>
                      <small>Uptime</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Video Demo Section */}
      <section className="video-section py-5" style={{ backgroundColor: '#f8f9fa' }}>
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-lg-10">
              <div className="text-center mb-5">
                <h2 className="display-5 fw-bold mb-3" style={{ color: '#002B59' }}>
                  See Our ATS in Action
                </h2>
                <p className="lead text-muted">
                  Take a comprehensive tour of our platform and discover how it can transform your recruitment process.
                </p>
              </div>
              
              {/* Custom Video Player */}
              <div 
                className="video-container position-relative rounded-4 overflow-hidden shadow-lg"
                style={{
                  background: '#000',
                  aspectRatio: '16/9'
                }}
              >
                <video
                  ref={videoRef}
                  className="w-100 h-100"
                  onTimeUpdate={handleTimeUpdate}
                  onLoadedMetadata={handleLoadedMetadata}
                  poster="https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                  style={{ objectFit: 'cover' }}
                  preload="metadata"
                >
                  <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4" />
                  <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.webm" type="video/webm" />
                  Your browser does not support the video tag.
                </video>
                
                {/* Custom Video Controls */}
                <div 
                  className="video-controls position-absolute bottom-0 start-0 end-0 p-3"
                  style={{
                    background: 'linear-gradient(transparent, rgba(0,0,0,0.7))',
                  }}
                >
                  <div className="d-flex align-items-center gap-3">
                    <button
                      className="btn btn-link text-white p-0"
                      onClick={handlePlayPause}
                      style={{ fontSize: '1.5rem' }}
                    >
                      {isPlaying ? <FaPause /> : <FaPlay />}
                    </button>
                    
                    <div className="flex-grow-1">
                      <div
                        className="progress"
                        style={{ height: '4px', cursor: 'pointer' }}
                        onClick={handleProgressClick}
                      >
                        <div
                          className="progress-bar"
                          style={{
                            width: `${duration ? (currentTime / duration) * 100 : 0}%`,
                            background: 'linear-gradient(90deg, #002B59 0%, #009DE6 100%)'
                          }}
                        />
                      </div>
                    </div>
                    
                    <span className="text-white small">
                      {formatTime(currentTime)} / {formatTime(duration)}
                    </span>
                    
                    <button className="btn btn-link text-white p-0">
                      <FaVolumeUp />
                    </button>
                    
                    <button className="btn btn-link text-white p-0">
                      <FaExpand />
                    </button>
                  </div>
                </div>
                
                {/* Play Button Overlay */}
                {!isPlaying && (
                  <div 
                    className="position-absolute top-50 start-50 translate-middle"
                    style={{ cursor: 'pointer' }}
                    onClick={handlePlayPause}
                  >
                    <div 
                      className="btn btn-primary rounded-circle d-flex align-items-center justify-content-center"
                      style={{
                        width: '80px',
                        height: '80px',
                        background: 'linear-gradient(90deg, #002B59 0%, #009DE6 100%)',
                        border: 'none',
                        boxShadow: '0 8px 25px rgba(0, 43, 89, 0.4)'
                      }}
                    >
                      <FaPlay size={24} style={{ marginLeft: '4px' }} />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features-section py-5">
        <div className="container">
          <div className="row">
            <div className="col-lg-6 mb-5">
              <h2 className="display-6 fw-bold mb-4" style={{ color: '#002B59' }}>
                Powerful Features That Drive Results
              </h2>
              <p className="lead text-muted mb-4">
                Our ATS platform combines cutting-edge technology with intuitive design to deliver
                exceptional hiring experiences for both recruiters and candidates.
              </p>

              <div className="row g-4">
                {features.map((feature, index) => (
                  <div key={index} className="col-md-6">
                    <div
                      className="feature-card h-100 p-4 rounded-3"
                      style={{
                        background: 'white',
                        border: '1px solid #e9ecef',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
                        transition: 'all 0.3s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'translateY(-5px)';
                        e.currentTarget.style.boxShadow = '0 8px 25px rgba(0, 43, 89, 0.15)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'translateY(0)';
                        e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.05)';
                      }}
                    >
                      <div
                        className="feature-icon mb-3 d-flex align-items-center justify-content-center rounded-circle"
                        style={{
                          width: '50px',
                          height: '50px',
                          background: 'linear-gradient(90deg, #002B59 0%, #009DE6 100%)',
                          color: 'white'
                        }}
                      >
                        <feature.icon size={20} />
                      </div>
                      <h5 className="fw-bold mb-2" style={{ color: '#002B59' }}>
                        {feature.title}
                      </h5>
                      <p className="text-muted small mb-0">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="col-lg-6">
              <div
                className="benefits-card p-5 rounded-4 h-100"
                style={{
                  background: 'linear-gradient(135deg, #002B59 0%, #009DE6 100%)',
                  color: 'white'
                }}
              >
                <h3 className="fw-bold mb-4">Why Choose Our ATS?</h3>
                <div className="benefits-list">
                  {benefits.map((benefit, index) => (
                    <div key={index} className="d-flex align-items-start mb-3">
                      <FaCheckCircle
                        className="me-3 mt-1 flex-shrink-0"
                        style={{ color: '#00ff88' }}
                        size={16}
                      />
                      <span style={{ fontSize: '15px', lineHeight: '1.5' }}>
                        {benefit}
                      </span>
                    </div>
                  ))}
                </div>

                <div className="mt-4 pt-4" style={{ borderTop: '1px solid rgba(255,255,255,0.2)' }}>
                  <h5 className="fw-bold mb-2">Ready to Get Started?</h5>
                  <p className="mb-3 opacity-75">
                    Join thousands of companies already using our platform to hire better, faster.
                  </p>
                  <button
                    className="btn btn-light btn-lg fw-semibold"
                    style={{
                      borderRadius: '10px',
                      padding: '12px 24px'
                    }}
                  >
                    Start Free Trial
                    <FaArrowRight className="ms-2" size={14} />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section
        className="cta-section py-5"
        style={{
          background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)'
        }}
      >
        <div className="container">
          <div className="row justify-content-center text-center">
            <div className="col-lg-8">
              <h2 className="display-6 fw-bold mb-4" style={{ color: '#002B59' }}>
                Ready to Transform Your Hiring Process?
              </h2>
              <p className="lead text-muted mb-5">
                Schedule a personalized demo and see how our ATS can streamline your recruitment workflow,
                improve candidate experience, and help you hire the best talent faster.
              </p>

              <div className="row g-4 justify-content-center">
                <div className="col-md-6">
                  <div
                    className="cta-card p-4 rounded-3 h-100"
                    style={{
                      background: 'white',
                      border: '2px solid #009DE6',
                      boxShadow: '0 8px 25px rgba(0, 157, 230, 0.15)'
                    }}
                  >
                    <FaPhone
                      className="mb-3"
                      style={{
                        color: '#009DE6',
                        fontSize: '2rem'
                      }}
                    />
                    <h4 className="fw-bold mb-3" style={{ color: '#002B59' }}>
                      Schedule a Call
                    </h4>
                    <p className="text-muted mb-4">
                      Speak with our experts and get a personalized walkthrough of the platform.
                    </p>
                    <button
                      className="btn btn-lg w-100 fw-semibold text-white"
                      style={{
                        background: 'linear-gradient(90deg, #002B59 0%, #009DE6 100%)',
                        border: 'none',
                        borderRadius: '10px',
                        padding: '12px'
                      }}
                    >
                      Book Demo Call
                    </button>
                  </div>
                </div>

                <div className="col-md-6">
                  <div
                    className="cta-card p-4 rounded-3 h-100"
                    style={{
                      background: 'white',
                      border: '2px solid #002B59',
                      boxShadow: '0 8px 25px rgba(0, 43, 89, 0.15)'
                    }}
                  >
                    <FaEnvelope
                      className="mb-3"
                      style={{
                        color: '#002B59',
                        fontSize: '2rem'
                      }}
                    />
                    <h4 className="fw-bold mb-3" style={{ color: '#002B59' }}>
                      Get Information
                    </h4>
                    <p className="text-muted mb-4">
                      Download our product brochure and pricing information.
                    </p>
                    <button
                      className="btn btn-outline-primary btn-lg w-100 fw-semibold"
                      style={{
                        borderColor: '#002B59',
                        color: '#002B59',
                        borderRadius: '10px',
                        padding: '12px',
                        borderWidth: '2px'
                      }}
                    >
                      Download Brochure
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ATSDemo;
